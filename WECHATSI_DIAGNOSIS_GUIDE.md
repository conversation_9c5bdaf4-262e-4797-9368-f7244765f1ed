# WechatSI 插件诊断实现指南

## 功能概述

已经实现了完整的 WechatSI 插件可用性检测和诊断功能，页面加载时自动执行诊断，帮助排查插件问题。

## 诊断流程

### 页面加载时自动执行
```javascript
onLoad() {
  this.initSystemInfo();
  this.startRecordTimer();
  this.initKeyboardListener();
  this.addWelcomeMessage();
  
  // 诊断 WechatSI 插件可用性
  this.diagnoseWechatSIPlugin();
}
```

### 诊断步骤

#### 步骤一：插件可用性检测
```javascript
checkPluginAvailability() {
  return new Promise((resolve) => {
    // 方法1：直接尝试引入
    try {
      const plugin = requirePlugin('WechatSI');
      if (plugin && typeof plugin.getRecordRecognitionManager === 'function') {
        console.log('✅ 插件功能正常');
        return resolve({ available: true, method: 'direct' });
      }
    } catch (e) {
      console.error('❌ 插件引入失败:', e);
    }

    // 方法2：通过API检查
    wx.getPluginManager().getPluginList({
      success: (res) => {
        const hasPlugin = res.pluginList.some(
          item => item.appid === 'wx069ba97219f66d99'
        );
        
        if (hasPlugin) {
          resolve({ available: false, reason: '插件已安装但无法调用' });
        } else {
          resolve({ available: false, reason: '插件未安装' });
        }
      },
      fail: (err) => {
        resolve({ available: false, reason: '无法获取插件列表' });
      }
    });
  });
}
```

#### 步骤二：安全封装插件调用
```javascript
getVoicePlugin() {
  return new Promise((resolve, reject) => {
    // 尝试官方引入方式
    try {
      const plugin = requirePlugin('WechatSI');
      if (plugin) return resolve(plugin);
    } catch (e) {}

    // 尝试备用引入方式（某些特殊环境）
    try {
      const plugin = requirePlugin('plugin://WechatSI');
      if (plugin) return resolve(plugin);
    } catch (e) {}

    // 终极fallback方案
    wx.request({
      url: 'https://mp.weixin.qq.com/mp/waerrpage?appid=wx069ba97219f66d99',
      success() {
        reject(new Error('插件存在但无法直接调用'));
      },
      fail() {
        reject(new Error('插件完全不可用'));
      }
    });
  });
}
```

#### 步骤三：综合诊断
```javascript
async diagnosePlugin() {
  const result = {
    timestamp: new Date().toISOString(),
    checks: [],
    summary: {
      available: false,
      canUse: false,
      recommendation: ''
    }
  };

  // 检查1：基础可用性
  const availabilityResult = await this.checkPluginAvailability();
  result.checks.push({
    name: '基础可用性检测',
    status: availabilityResult.available ? 'pass' : 'fail',
    details: availabilityResult
  });

  // 检查2：插件获取
  try {
    const plugin = await this.getVoicePlugin();
    result.checks.push({
      name: '插件获取测试',
      status: 'pass',
      details: { hasPlugin: !!plugin }
    });
    result.summary.canUse = true;
  } catch (e) {
    result.checks.push({
      name: '插件获取测试',
      status: 'fail',
      error: e.message
    });
  }

  // 检查3：录音识别管理器
  if (result.summary.canUse) {
    try {
      const manager = WechatSI.getRecordRecognitionManager();
      result.checks.push({
        name: '录音识别管理器',
        status: manager ? 'pass' : 'fail'
      });
    } catch (e) {
      result.summary.canUse = false;
    }
  }

  return result;
}
```

## 诊断结果展示

### 1. 弹窗显示
```javascript
showDiagnosisResult(result) {
  const details = result.checks.map(check => 
    `${check.name}: ${check.status === 'pass' ? '✅' : '❌'}`
  ).join('\n');
  
  wx.showModal({
    title: 'WechatSI 插件诊断',
    content: `${result.summary.recommendation}\n\n详细检查:\n${details}`,
    showCancel: true,
    confirmText: '查看详情',
    success: (res) => {
      if (res.confirm) {
        this.showDetailedDiagnosis(result);
      }
    }
  });
}
```

### 2. 聊天消息提示
```javascript
// 插件不可用时
this.addMessage({
  type: 'bot',
  content: `⚠️ WechatSI 语音识别插件检测异常\n\n${result.summary.recommendation}\n\n当前将使用备用的语音录制功能。`,
  timestamp: new Date().toLocaleString()
});

// 插件正常时
this.addMessage({
  type: 'bot',
  content: '✅ WechatSI 语音识别插件工作正常，您可以使用长按录音功能进行语音交互！',
  timestamp: new Date().toLocaleString()
});
```

### 3. 详细诊断信息
```javascript
showDetailedDiagnosis(result) {
  const detailText = JSON.stringify(result, null, 2);
  
  // 复制到剪贴板
  wx.setClipboardData({
    data: detailText,
    success: () => {
      wx.showToast({
        title: '诊断信息已复制',
        icon: 'success'
      });
    }
  });
}
```

## 可能的诊断结果

### 1. 插件完全可用 ✅
```json
{
  "summary": {
    "available": true,
    "canUse": true,
    "recommendation": "✅ WechatSI 插件完全可用，可以正常使用语音识别功能"
  },
  "checks": [
    { "name": "基础可用性检测", "status": "pass" },
    { "name": "插件获取测试", "status": "pass" },
    { "name": "录音识别管理器", "status": "pass" }
  ]
}
```

### 2. 插件已安装但无法调用 ⚠️
```json
{
  "summary": {
    "available": true,
    "canUse": false,
    "recommendation": "⚠️ 插件已安装但无法正常调用，请检查配置或联系开发者"
  },
  "checks": [
    { "name": "基础可用性检测", "status": "fail" },
    { "name": "插件获取测试", "status": "fail" }
  ]
}
```

### 3. 插件未安装 ❌
```json
{
  "summary": {
    "available": false,
    "canUse": false,
    "recommendation": "❌ 插件不可用，请在小程序管理后台添加同声传译插件"
  },
  "checks": [
    { "name": "基础可用性检测", "status": "fail", "reason": "插件未安装" }
  ]
}
```

## 常见问题排查

### 1. 插件配置问题
**现象**：插件引入失败
**原因**：app.json 中插件配置错误
**解决**：检查 plugins 配置
```json
{
  "plugins": {
    "WechatSI": {
      "version": "0.0.7",
      "provider": "wx069ba97219f66d99"
    }
  }
}
```

### 2. 权限问题
**现象**：插件可用但录音失败
**原因**：缺少录音权限
**解决**：检查 permission 配置
```json
{
  "permission": {
    "scope.record": {
      "desc": "需要您的授权以便录制语音"
    }
  }
}
```

### 3. 网络问题
**现象**：录音成功但识别失败
**原因**：网络连接问题
**解决**：检查网络连接，重试

### 4. 版本兼容问题
**现象**：某些方法不存在
**原因**：插件版本过旧
**解决**：更新插件版本

## 测试验证

### 1. 页面加载测试
- [ ] 打开页面时自动执行诊断
- [ ] 显示诊断结果弹窗
- [ ] 在聊天中显示状态消息

### 2. 插件功能测试
- [ ] 插件可用时正常录音识别
- [ ] 插件不可用时显示错误提示
- [ ] 降级方案正常工作

### 3. 诊断信息测试
- [ ] 点击"查看详情"复制诊断信息
- [ ] 诊断信息格式正确
- [ ] 包含所有必要的调试信息

## 购买流程测试

### 1. 点击欢迎语测试
- [ ] 点击"上一次买的快用完了，再来一单吧！"
- [ ] 触发购买流程
- [ ] 显示商品卡片

### 2. 完整购买流程
- [ ] 商品卡片显示正确
- [ ] 点击"查看价格并支付"
- [ ] 收银台正常显示
- [ ] 支付流程完整

## 日志输出示例

```
🔍 开始诊断 WechatSI 插件...
开始检测 WechatSI 插件可用性...
插件对象: [object Object]
✅ 插件功能正常
✅ 官方方式引入成功
🔍 诊断完成: {
  "timestamp": "2024-01-01T12:00:00.000Z",
  "summary": {
    "available": true,
    "canUse": true,
    "recommendation": "✅ WechatSI 插件完全可用"
  }
}
```

## 总结

WechatSI 插件诊断功能特点：

1. ✅ **自动执行** - 页面加载时自动诊断
2. ✅ **多重检测** - 基础可用性、插件获取、管理器测试
3. ✅ **详细报告** - 完整的诊断结果和建议
4. ✅ **用户友好** - 弹窗提示和聊天消息
5. ✅ **开发者友好** - 详细日志和调试信息
6. ✅ **降级处理** - 插件不可用时的备用方案

现在可以准确诊断 WechatSI 插件的可用性状态，帮助快速定位和解决问题！🔍✨
