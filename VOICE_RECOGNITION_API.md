# 微信小程序语音识别API集成

## 功能更新

已将语音识别功能从模拟实现改为调用微信小程序的真实语音识别API，提供更准确的语音转文字服务。

## API说明

### 使用的微信API

#### 1. wx.translateVoice
```javascript
wx.translateVoice({
  filePath: string,           // 语音文件路径
  isShowProgressTips: number, // 是否显示进度提示 (0/1)
  success: function,          // 成功回调
  fail: function             // 失败回调
})
```

#### 2. 参数说明
- **filePath**: 录音文件的临时路径
- **isShowProgressTips**: 1表示显示"正在识别"的进度提示
- **success**: 识别成功回调，返回识别结果
- **fail**: 识别失败回调，返回错误信息

## 实现特性

### 1. 智能降级机制
```javascript
// 检查API支持
if (!wx.startRecord || !wx.stopRecord) {
  // 降级到模拟实现
  this.fallbackSpeechToText().then(resolve).catch(reject);
  return;
}

// 网络错误时自动降级
if (err.errMsg && err.errMsg.includes('network')) {
  console.log('网络问题，降级到模拟语音识别');
  this.fallbackSpeechToText().then(resolve).catch(reject);
}
```

### 2. 错误处理
```javascript
// 根据错误信息提供友好提示
let errorMessage = '语音识别失败';
if (err.errMsg) {
  if (err.errMsg.includes('network')) {
    errorMessage = '网络连接失败，请检查网络';
  } else if (err.errMsg.includes('timeout')) {
    errorMessage = '识别超时，请重试';
  } else if (err.errMsg.includes('format')) {
    errorMessage = '音频格式不支持';
  } else if (err.errMsg.includes('duration')) {
    errorMessage = '音频时长不符合要求';
  }
}
```

### 3. 用户体验优化
- **加载提示**: 显示"语音识别中..."加载框
- **结果反馈**: 区分真实识别和模拟识别
- **置信度显示**: 显示识别准确度百分比
- **错误处理**: 详细的错误提示和处理建议

## 界面更新

### 1. 识别结果标签
```xml
<view class="transcript-tag">
  <text class="tag-text">{{item.isFallback ? '模拟识别' : '语音转文字'}}</text>
  <view wx:if="{{item.confidence}}" class="confidence-indicator">
    <text class="confidence-text">{{Math.round(item.confidence * 100)}}%</text>
  </view>
</view>
```

### 2. 状态提示
- ✅ **识别成功**: "识别成功" / "模拟识别完成"
- ❌ **识别失败**: 详细错误信息弹窗
- ⚠️ **结果为空**: "未识别到语音内容"

## 技术细节

### 1. 录音格式要求
- **格式**: MP3
- **采样率**: 16000Hz
- **声道数**: 1 (单声道)
- **编码码率**: 96000
- **时长限制**: 1-60秒

### 2. 网络要求
- 需要网络连接
- 支持WiFi和移动网络
- 自动重试机制

### 3. 权限要求
- 录音权限 (`scope.record`)
- 网络访问权限

## 使用流程

### 1. 正常流程
```
用户长按录音 → 录音完成 → 调用wx.translateVoice → 显示识别结果 → AI回复
```

### 2. 降级流程
```
API不支持/网络错误 → 降级到模拟识别 → 显示模拟结果 → AI回复
```

### 3. 错误处理
```
识别失败 → 显示错误信息 → 用户可重试
```

## 配置说明

### 1. 小程序配置
在 `app.json` 中确保有网络权限：
```json
{
  "permission": {
    "scope.record": {
      "desc": "用于语音识别功能"
    }
  }
}
```

### 2. 服务器域名配置
需要在小程序后台配置微信服务器域名（微信会自动处理）

## 测试建议

### 1. 功能测试
- ✅ 正常语音识别
- ✅ 网络异常时的降级
- ✅ API不支持时的降级
- ✅ 各种错误情况的处理

### 2. 兼容性测试
- ✅ 不同微信版本
- ✅ 不同设备型号
- ✅ 不同网络环境

### 3. 用户体验测试
- ✅ 识别准确度
- ✅ 响应速度
- ✅ 错误提示友好性

## 注意事项

### 1. API限制
- 微信小程序语音识别有调用频率限制
- 单次识别时长限制为60秒
- 需要真机测试，开发者工具可能不支持

### 2. 网络依赖
- 语音识别需要网络连接
- 网络不稳定时会影响识别效果
- 建议在网络良好的环境下使用

### 3. 隐私保护
- 语音数据会上传到微信服务器进行识别
- 符合微信隐私政策
- 不会永久存储语音数据

## 后续优化

### 1. 识别准确度
- 可以考虑接入其他语音识别服务
- 支持方言识别
- 支持多语言识别

### 2. 功能扩展
- 实时语音识别
- 语音指令识别
- 语音情感分析

### 3. 性能优化
- 音频压缩
- 缓存机制
- 批量识别

这次更新让语音识别功能更加实用和可靠！
