# 长按录音功能测试指南

## 功能更新说明

1. **交互方式**: 已将语音录音功能从"点击开始/停止"改为"长按录音"的交互方式
2. **语音识别**: 集成微信小程序真实语音识别API (`wx.translateVoice`)
3. **智能降级**: API不可用时自动降级到模拟实现
4. **用户体验**: 添加置信度显示、详细错误提示等

## 测试步骤

### 1. 基本录音测试

1. **进入语音对话页面**
   - 访问 `/pages/voice-chat/index`
   - 或通过悬浮窗按钮跳转

2. **长按录音测试**
   - 长按语音按钮（🎤）
   - 观察按钮变化：🎤 → 🔴
   - 观察状态提示："正在录音...Xs"
   - 感受震动反馈

3. **正常发送测试**
   - 录音超过1秒后松开按钮
   - 观察"发送成功"提示
   - 检查语音消息是否出现在聊天列表

### 2. 边界情况测试

1. **短按测试**
   - 快速点击语音按钮（不长按）
   - 应显示"请长按录音"提示

2. **录音时间过短测试**
   - 长按开始录音后立即松开（<1秒）
   - 应显示"录音时间太短"提示
   - 录音不应保存

3. **取消录音测试**
   - 长按开始录音
   - 手指移出按钮区域
   - 应显示"录音已取消"提示
   - 录音不应保存

### 3. 权限测试

1. **首次使用测试**
   - 清除小程序数据
   - 首次长按录音
   - 应弹出录音权限申请

2. **权限拒绝测试**
   - 拒绝录音权限
   - 长按录音按钮
   - 应显示权限设置引导

### 4. 语音转文字测试

1. **真实API测试**
   - 录音完成后显示"语音识别中..."
   - 应出现"语音转文字"标签的消息
   - 显示置信度百分比（如"95%"）
   - 随后出现AI回复

2. **降级机制测试**
   - 在网络不佳环境下测试
   - 应自动降级到"模拟识别"
   - 显示"模拟识别完成"提示

3. **错误处理测试**
   - 断网状态下录音
   - 应显示详细错误信息弹窗
   - 提供重试建议

### 5. 语音播放测试

1. **点击语音消息**
   - 点击已发送的语音消息
   - 观察播放状态变化
   - 检查播放图标动画

## 预期行为

### 正常流程
```
长按按钮 → 延迟500ms → 开始录音 → 震动反馈 → 录音中状态 → 松开按钮 → 发送语音 → 震动反馈 → 真实语音识别 → 显示置信度 → AI回复
```

### 降级流程
```
长按录音 → API不可用/网络错误 → 自动降级 → 模拟识别 → 显示"模拟识别"标签 → AI回复
```

### 状态变化
```
🎤 "长按说话" → 🔴 "正在录音...Xs" → ✅ "发送成功"
```

### 异常处理
- 权限拒绝 → 引导设置
- 录音过短 → 提示并取消
- 手指移出 → 取消录音
- 录音失败 → 错误提示

## 注意事项

1. **测试环境**
   - 需要在真机上测试（模拟器可能不支持录音）
   - 确保设备有麦克风权限

2. **网络要求**
   - 语音转文字功能需要网络（当前为模拟实现）

3. **性能考虑**
   - 长时间录音可能影响性能
   - 建议测试60秒最大录音时长

## 已知问题

1. ✅ ~~语音转文字为模拟实现~~ → 已集成微信真实API
2. AI回复为模拟数据，需接入真实AI服务
3. 上滑取消手势暂未实现，可在后续版本添加
4. 语音识别依赖网络，离线状态下会降级到模拟实现

## 反馈收集

测试时请关注：
- 交互是否流畅自然
- 状态提示是否清晰
- 震动反馈是否合适
- 错误处理是否友好
- 性能是否稳定

如有问题或建议，请记录具体的操作步骤和预期行为。
