# 长按录音功能测试指南

## 功能更新说明

已将语音录音功能从"点击开始/停止"改为"长按录音"的交互方式，更符合用户习惯。

## 测试步骤

### 1. 基本录音测试

1. **进入语音对话页面**
   - 访问 `/pages/voice-chat/index`
   - 或通过悬浮窗按钮跳转

2. **长按录音测试**
   - 长按语音按钮（🎤）
   - 观察按钮变化：🎤 → 🔴
   - 观察状态提示："正在录音...Xs"
   - 感受震动反馈

3. **正常发送测试**
   - 录音超过1秒后松开按钮
   - 观察"发送成功"提示
   - 检查语音消息是否出现在聊天列表

### 2. 边界情况测试

1. **短按测试**
   - 快速点击语音按钮（不长按）
   - 应显示"请长按录音"提示

2. **录音时间过短测试**
   - 长按开始录音后立即松开（<1秒）
   - 应显示"录音时间太短"提示
   - 录音不应保存

3. **取消录音测试**
   - 长按开始录音
   - 手指移出按钮区域
   - 应显示"录音已取消"提示
   - 录音不应保存

### 3. 权限测试

1. **首次使用测试**
   - 清除小程序数据
   - 首次长按录音
   - 应弹出录音权限申请

2. **权限拒绝测试**
   - 拒绝录音权限
   - 长按录音按钮
   - 应显示权限设置引导

### 4. 语音转文字测试

1. **录音完成后**
   - 等待2秒左右
   - 应出现"[语音转文字]"消息
   - 随后出现AI回复

### 5. 语音播放测试

1. **点击语音消息**
   - 点击已发送的语音消息
   - 观察播放状态变化
   - 检查播放图标动画

## 预期行为

### 正常流程
```
长按按钮 → 延迟500ms → 开始录音 → 震动反馈 → 录音中状态 → 松开按钮 → 发送语音 → 震动反馈 → 语音转文字 → AI回复
```

### 状态变化
```
🎤 "长按说话" → 🔴 "正在录音...Xs" → ✅ "发送成功"
```

### 异常处理
- 权限拒绝 → 引导设置
- 录音过短 → 提示并取消
- 手指移出 → 取消录音
- 录音失败 → 错误提示

## 注意事项

1. **测试环境**
   - 需要在真机上测试（模拟器可能不支持录音）
   - 确保设备有麦克风权限

2. **网络要求**
   - 语音转文字功能需要网络（当前为模拟实现）

3. **性能考虑**
   - 长时间录音可能影响性能
   - 建议测试60秒最大录音时长

## 已知问题

1. 语音转文字为模拟实现，实际项目需接入真实API
2. AI回复为模拟数据，需接入真实AI服务
3. 上滑取消手势暂未实现，可在后续版本添加

## 反馈收集

测试时请关注：
- 交互是否流畅自然
- 状态提示是否清晰
- 震动反馈是否合适
- 错误处理是否友好
- 性能是否稳定

如有问题或建议，请记录具体的操作步骤和预期行为。
