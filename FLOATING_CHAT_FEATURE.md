# 悬浮窗语音对话功能

## 功能概述

已成功创建了一个完整的悬浮窗语音对话功能，包含以下核心组件：

### 1. 悬浮窗组件 (`src/components/floating-chat-button/`)
- **功能**: 可拖拽的悬浮窗按钮，支持自动吸附边缘
- **特性**: 
  - 🎨 渐变背景 + 呼吸动画效果
  - 🖱️ 支持触摸拖拽移动
  - 📍 自动吸附到屏幕边缘
  - 💫 点击跳转到语音对话页面

### 2. 语音对话页面 (`src/pages/voice-chat/`)
- **功能**: 完整的聊天对话界面
- **特性**:
  - 💬 文本消息输入和发送
  - 🎤 语音录制和播放
  - 🔄 语音转文字功能（模拟实现）
  - 🤖 AI 模拟回复
  - 💾 聊天记录本地存储
  - 📱 响应式设计，适配不同屏幕

### 3. 语音处理工具 (`src/pages/voice-chat/voice-utils.js`)
- **功能**: 封装语音录制、播放、转文字等功能
- **特性**:
  - 🔐 自动权限检查和申请
  - ⏱️ 录音时长控制
  - 🎵 音频播放管理
  - 📝 语音转文字接口（可扩展真实API）

### 4. 全局管理器 (`src/utils/floating-chat-manager.js`)
- **功能**: 统一管理所有页面的悬浮窗显示
- **特性**:
  - 🌐 全局启用/禁用控制
  - 📋 排除页面列表管理
  - 💾 设置本地存储
  - 🔄 页面生命周期监听

### 5. 页面混入 (`src/mixins/floating-chat-mixin.js`)
- **功能**: 为页面提供悬浮窗功能的便捷混入
- **特性**:
  - 🔧 即插即用的混入模式
  - 📍 位置状态管理
  - 🎛️ 显示/隐藏控制方法

## 文件结构

```
src/
├── components/
│   └── floating-chat-button/          # 悬浮窗组件
│       ├── index.js                   # 组件逻辑
│       ├── index.wxml                 # 组件模板
│       ├── index.wxss                 # 组件样式
│       ├── index.json                 # 组件配置
│       └── README.md                  # 使用说明
├── pages/
│   ├── voice-chat/                    # 语音对话页面
│   │   ├── index.js                   # 页面逻辑
│   │   ├── index.wxml                 # 页面模板
│   │   ├── index.wxss                 # 页面样式
│   │   ├── index.json                 # 页面配置
│   │   └── voice-utils.js             # 语音工具类
│   └── demo-floating-chat/            # 演示页面
│       ├── index.js
│       ├── index.wxml
│       ├── index.wxss
│       └── index.json
├── utils/
│   └── floating-chat-manager.js       # 全局管理器
├── mixins/
│   └── floating-chat-mixin.js         # 页面混入
└── app.json                           # 已添加组件和页面配置
```

## 使用方法

### 快速开始

1. **在任意页面中使用悬浮窗**:

```javascript
// 页面 JS 文件
const floatingChatMixin = require('../../mixins/floating-chat-mixin');

Page({
  ...floatingChatMixin,
  data: {
    ...floatingChatMixin.data,
    // 你的页面数据
  },
  onLoad(options) {
    floatingChatMixin.onLoad.call(this, options);
  },
  onShow() {
    floatingChatMixin.onShow.call(this);
  }
});
```

```xml
<!-- 页面 WXML 文件 -->
<floating-chat-button 
  wx:if="{{floatingChatVisible}}"
  show="{{floatingChatVisible}}"
  position="{{floatingChatPosition}}"
  bindtap="onFloatingChatTap"
/>
```

2. **访问演示页面**: `/pages/demo-floating-chat/index`

3. **直接访问语音对话**: `/pages/voice-chat/index`

### 全局控制

```javascript
const floatingChatManager = require('../utils/floating-chat-manager');

// 全局禁用悬浮窗
floatingChatManager.disable();

// 添加不显示悬浮窗的页面
floatingChatManager.addExcludePage('pages/special-page/index');
```

## 技术特点

### 1. 响应式设计
- 适配不同屏幕尺寸
- 自动边缘吸附算法
- 触摸友好的交互设计

### 2. 性能优化
- 组件按需加载
- 本地存储缓存
- 事件防抖处理

### 3. 用户体验
- 流畅的动画效果
- 直观的操作反馈
- 智能的权限处理

### 4. 可扩展性
- 模块化设计
- 插件式架构
- 易于自定义样式

## 后续扩展建议

1. **接入真实语音识别API**
   - 替换 `voice-utils.js` 中的模拟实现
   - 支持更多语言和方言

2. **集成真实AI对话**
   - 接入ChatGPT、文心一言等AI服务
   - 支持上下文对话

3. **增强功能**
   - 支持图片、文件发送
   - 添加表情包功能
   - 支持语音消息转发

4. **性能优化**
   - 虚拟列表优化长对话
   - 音频文件压缩
   - 离线缓存机制

## 注意事项

1. **权限要求**: 需要录音权限 (`scope.record`)
2. **兼容性**: 支持微信小程序基础库 2.0+
3. **存储**: 使用本地存储保存聊天记录和设置
4. **网络**: 语音转文字功能需要网络连接（实际API）

## 测试建议

1. 在不同页面测试悬浮窗显示
2. 测试拖拽和吸附功能
3. 测试语音录制和播放
4. 测试权限申请流程
5. 测试聊天记录存储和恢复

这个功能已经完全集成到项目中，可以立即使用！
