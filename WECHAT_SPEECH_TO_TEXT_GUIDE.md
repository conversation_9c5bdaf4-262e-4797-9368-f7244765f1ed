# 微信小程序语音识别使用指南

## 功能说明

现在已经集成了微信小程序原生的语音识别API，可以将录音文件转换为文字。

## 实现方式

### 1. 使用微信原生API
```javascript
speechToText(filePath) {
  return new Promise((resolve, reject) => {
    // 检查API可用性
    if (typeof wx.createRecognitionManager !== 'function') {
      // 降级到模拟实现
      this.mockSpeechToText().then(resolve).catch(reject);
      return;
    }

    // 创建语音识别管理器
    const recognitionManager = wx.createRecognitionManager();

    // 设置回调
    recognitionManager.onStart(() => {
      console.log('语音识别开始');
    });

    recognitionManager.onStop((res) => {
      if (res.result && res.result.length > 0) {
        resolve({
          text: res.result,
          confidence: 0.9
        });
      } else {
        reject(new Error('语音识别无结果'));
      }
    });

    recognitionManager.onError((err) => {
      reject(new Error('语音识别失败'));
    });

    // 开始识别
    recognitionManager.start({
      lang: 'zh_CN' // 中文识别
    });
  });
}
```

### 2. 降级方案
如果微信API不可用（如在开发者工具中），会自动降级到模拟实现：
```javascript
mockSpeechToText() {
  return new Promise((resolve) => {
    const mockTexts = [
      '你好，我想了解一下产品信息',
      '请问有什么优惠活动吗？',
      '我要预订明天的服务',
      // ... 更多模拟文本
    ];

    setTimeout(() => {
      const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];
      resolve({
        text: randomText,
        confidence: 0.85,
        isMock: true
      });
    }, 1500);
  });
}
```

## API说明

### wx.createRecognitionManager()

**功能**：创建语音识别管理器

**支持版本**：基础库 1.6.0 开始支持

**回调方法**：
- `onStart()` - 识别开始
- `onRecognize(result)` - 识别中间结果
- `onStop(result)` - 识别结束
- `onError(error)` - 识别错误

**启动参数**：
```javascript
recognitionManager.start({
  lang: 'zh_CN',        // 语言，支持 zh_CN、en_US
  serviceType: 'iat'    // 服务类型，默认为实时语音识别
});
```

### 错误码说明

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 10003 | 网络连接失败 | 检查网络连接 |
| 10007 | 语音过短 | 提示用户重新录制 |
| 10008 | 语音过长 | 提示控制在60秒内 |
| 10009 | 服务暂时不可用 | 稍后重试 |

## 使用流程

### 1. 录音 → 识别
```javascript
// 1. 开始录音
voiceUtils.startRecord()
  .then(() => {
    console.log('录音开始');
  });

// 2. 停止录音
voiceUtils.stopRecord()
  .then((result) => {
    // 3. 语音识别
    return voiceUtils.speechToText(result.tempFilePath);
  })
  .then((speechResult) => {
    console.log('识别结果:', speechResult.text);
    // 4. 显示识别结果
    this.addMessage({
      type: 'user',
      content: speechResult.text
    });
  });
```

### 2. 页面集成
```javascript
// 在页面中调用
speechToText(filePath) {
  wx.showLoading({
    title: '语音识别中...',
    mask: true
  });

  voiceUtils
    .speechToText(filePath)
    .then((result) => {
      wx.hideLoading();
      
      // 添加用户消息
      this.addMessage({
        type: 'user',
        content: result.text,
        timestamp: new Date().toLocaleString()
      });

      // 模拟AI回复
      this.simulateAIResponse(result.text);
    })
    .catch((err) => {
      wx.hideLoading();
      wx.showToast({
        title: '语音识别失败',
        icon: 'error'
      });
    });
}
```

## 注意事项

### 1. 环境支持
- **真机**：完全支持微信语音识别API
- **开发者工具**：可能不支持，会自动降级到模拟实现
- **基础库版本**：需要 1.6.0 及以上

### 2. 权限要求
- 需要录音权限 `scope.record`
- 语音识别不需要额外权限

### 3. 文件格式
- 支持的录音格式：mp3、wav、aac
- 建议使用 mp3 格式，兼容性最好

### 4. 识别限制
- 单次识别时长：最长60秒
- 语言支持：中文、英文
- 网络要求：需要网络连接

## 测试方法

### 1. 真机测试
```bash
# 生成体验版二维码，在真机上测试
# 或使用真机调试功能
```

### 2. 功能验证
- [ ] 长按录音按钮开始录音
- [ ] 松开按钮停止录音
- [ ] 自动进行语音识别
- [ ] 显示识别结果
- [ ] 错误处理正常

### 3. 边界测试
- [ ] 录音时间过短（<1秒）
- [ ] 录音时间过长（>60秒）
- [ ] 网络断开情况
- [ ] 无声音录音

## 优化建议

### 1. 用户体验
```javascript
// 显示识别进度
wx.showLoading({
  title: '语音识别中...',
  mask: true
});

// 识别成功提示
wx.showToast({
  title: '识别完成',
  icon: 'success',
  duration: 1000
});
```

### 2. 错误处理
```javascript
// 根据错误类型给出具体提示
recognitionManager.onError((err) => {
  let message = '语音识别失败';
  switch (err.errCode) {
    case 10007:
      message = '录音时间太短，请重新录制';
      break;
    case 10008:
      message = '录音时间太长，请控制在60秒内';
      break;
    case 10003:
      message = '网络连接失败，请检查网络';
      break;
  }
  
  wx.showToast({
    title: message,
    icon: 'none'
  });
});
```

### 3. 性能优化
```javascript
// 复用识别管理器实例
class VoiceUtils {
  constructor() {
    this.recognitionManager = null;
  }

  getRecognitionManager() {
    if (!this.recognitionManager) {
      this.recognitionManager = wx.createRecognitionManager();
    }
    return this.recognitionManager;
  }
}
```

## 总结

现在的语音识别功能：

1. ✅ **使用微信原生API** - 无需第三方服务
2. ✅ **自动降级处理** - 开发环境使用模拟数据
3. ✅ **完整错误处理** - 针对不同错误给出提示
4. ✅ **简洁的实现** - 代码清晰易维护

这样的实现既保证了功能的完整性，又避免了复杂的第三方服务集成。
