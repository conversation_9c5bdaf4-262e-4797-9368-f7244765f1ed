# 购买流程实现指南

## 功能概述

已经根据你的需求实现了完整的购买流程，包括：

1. **语音触发购买** - 识别"上一次买的快用完了，再来一单吧！"等购买意图
2. **商品卡片展示** - 显示商品信息和"查看价格并支付"按钮
3. **收银台界面** - 仿照微信支付界面的收银台
4. **支付确认** - 模拟支付流程
5. **订单确认** - 支付成功后显示订单卡片

## 核心组件

### 1. 商品卡片组件 (product-card)

**文件位置**：
- `src/pages/voice-chat/components/product-card.wxml`
- `src/pages/voice-chat/components/product-card.wxss`
- `src/pages/voice-chat/components/product-card.js`

**功能特性**：
- 支持两种模式：购买模式 (`purchase`) 和订单模式 (`order`)
- 购买模式：显示"修改商品规格"和"查看价格并支付"按钮
- 订单模式：显示"查看订单详情"按钮
- 商品信息展示：图片、名称、规格、数量等

### 2. 收银台组件 (cashier)

**文件位置**：
- `src/pages/voice-chat/components/cashier.wxml`
- `src/pages/voice-chat/components/cashier.wxss`
- `src/pages/voice-chat/components/cashier.js`

**功能特性**：
- 仿照图片2的收银台界面设计
- 支持多种支付方式选择（花呗、银行卡、余额宝）
- 模拟支付流程
- 支付成功后触发回调事件

## 交互流程

### 1. 语音触发购买
```
用户说："上一次买的快用完了，再来一单吧！"
    ↓
语音识别 (WechatSI)
    ↓
AI识别购买意图
    ↓
显示商品卡片（购买模式）
```

### 2. 查看价格并支付
```
用户点击"查看价格并支付"
    ↓
显示收银台界面
    ↓
用户选择支付方式
    ↓
点击"确认付款"
    ↓
模拟支付过程（2秒）
    ↓
支付成功
```

### 3. 订单确认
```
支付成功
    ↓
隐藏收银台
    ↓
显示"支付成功"提示
    ↓
AI发送订单确认消息
    ↓
显示订单卡片（订单模式）
```

## 代码实现

### 1. 购买意图识别

```javascript
// 判断是否是购买相关消息
isPurchaseMessage(message) {
  const purchaseKeywords = [
    '上一次买的快用完了',
    '再来一单',
    '再买一次',
    '重复购买',
    '老样子',
    '还是上次那个',
    '再来一份'
  ];

  return purchaseKeywords.some(keyword => message.includes(keyword));
}

// 处理购买流程
handlePurchaseFlow(userMessage) {
  // 添加AI回复
  this.addMessage({
    type: 'bot',
    content: '好的，我为您找到了上次购买的商品，请确认订单信息：',
    timestamp: new Date().toLocaleString()
  });

  // 显示商品卡片
  setTimeout(() => {
    const mockProduct = {
      storeName: '蜜雪冰城（凤新南路店）',
      name: '冰鲜柠檬水',
      image: '/images/lemon-water.jpg',
      calories: '180kcal',
      specs: '规格：少冰 五分糖',
      quantity: 1,
      hasAudio: true
    };

    this.showProductCardWithData(mockProduct, 'purchase');
  }, 1500);
}
```

### 2. 商品卡片显示

```javascript
// 显示商品卡片
showProductCardWithData(product, type = 'purchase') {
  this.setData({
    currentProduct: product,
    productCardType: type,
    showProductCard: true
  });
}
```

### 3. 收银台处理

```javascript
// 查看价格并支付
onViewPriceAndPay(e) {
  const product = e.detail.product;
  
  // 构建订单信息
  const orderInfo = {
    storeName: product.storeName,
    amount: '4.00',
    hasAudio: product.hasAudio,
    product: product
  };

  // 显示收银台
  this.setData({
    currentOrder: orderInfo,
    showCashier: true,
    showProductCard: false
  });
}

// 支付成功处理
onPaymentSuccess(e) {
  const { orderInfo, paymentMethod } = e.detail;
  
  // 隐藏收银台
  this.setData({
    showCashier: false,
    showProductCard: false
  });

  // 显示支付成功提示
  wx.showToast({
    title: '支付成功',
    icon: 'success',
    duration: 2000
  });

  // 延迟显示订单确认
  setTimeout(() => {
    // 构建订单信息
    const orderProduct = {
      ...orderInfo.product,
      orderId: 'ORDER_' + Date.now(),
      orderTime: new Date().toLocaleString(),
      paymentMethod: paymentMethod.name
    };

    // 添加AI确认消息
    this.addMessage({
      type: 'bot',
      content: '支付成功！您的订单已确认，以下是订单详情：',
      timestamp: new Date().toLocaleString()
    });

    // 显示订单卡片
    this.showProductCardWithData(orderProduct, 'order');
  }, 2500);
}
```

## 界面设计

### 1. 商品卡片样式

参照图片1的设计：
- 白色卡片背景，圆角边框
- 顶部店铺名称和播放图标
- 中间商品图片和详细信息
- 底部操作按钮

### 2. 收银台样式

参照图片2的设计：
- 底部弹出式设计
- 顶部关闭按钮和"使用密码"
- 店铺信息和金额显示
- 支付方式选择列表
- 底部确认支付按钮

## 数据结构

### 1. 商品数据
```javascript
{
  storeName: '蜜雪冰城（凤新南路店）',
  name: '冰鲜柠檬水',
  image: '/images/lemon-water.jpg',
  calories: '180kcal',
  specs: '规格：少冰 五分糖',
  quantity: 1,
  hasAudio: true
}
```

### 2. 订单数据
```javascript
{
  storeName: '蜜雪冰城（凤新南路店）',
  amount: '4.00',
  hasAudio: true,
  product: { /* 商品信息 */ }
}
```

### 3. 支付方式数据
```javascript
[
  {
    id: 'huabei',
    name: '花呗',
    icon: '/images/huabei-icon.png',
    selected: true
  },
  {
    id: 'icbc',
    name: '工商银行储蓄卡 (7980)',
    icon: '/images/icbc-icon.png',
    selected: false
  },
  {
    id: 'yuebao',
    name: '余额宝',
    icon: '/images/yuebao-icon.png',
    selected: false
  }
]
```

## 测试场景

### 1. 语音购买测试
- [ ] 说"上一次买的快用完了，再来一单吧！"
- [ ] AI识别购买意图
- [ ] 显示商品卡片
- [ ] 商品信息正确显示

### 2. 支付流程测试
- [ ] 点击"查看价格并支付"
- [ ] 收银台正确显示
- [ ] 支付方式可以选择
- [ ] 点击"确认付款"
- [ ] 支付成功提示

### 3. 订单确认测试
- [ ] 支付成功后收银台消失
- [ ] AI发送确认消息
- [ ] 显示订单卡片
- [ ] 点击"查看订单详情"跳转

### 4. 边界情况测试
- [ ] 点击收银台外部关闭
- [ ] 点击关闭按钮
- [ ] 修改商品规格功能
- [ ] 网络异常处理

## 扩展功能

### 1. 真实数据集成
- 连接真实的商品API
- 集成真实的支付接口
- 订单系统对接

### 2. 更多购买场景
- 新商品推荐
- 优惠券使用
- 会员折扣

### 3. 语音识别优化
- 更多购买意图关键词
- 商品名称识别
- 数量和规格识别

## 总结

实现的购买流程特点：

1. ✅ **语音触发** - 自然语言识别购买意图
2. ✅ **界面美观** - 参照微信支付界面设计
3. ✅ **流程完整** - 从商品展示到支付确认
4. ✅ **交互自然** - 符合用户使用习惯
5. ✅ **组件化设计** - 可复用的商品卡片和收银台
6. ✅ **状态管理** - 完整的状态流转控制

现在用户可以通过语音"上一次买的快用完了，再来一单吧！"触发完整的购买流程！🛒✨
