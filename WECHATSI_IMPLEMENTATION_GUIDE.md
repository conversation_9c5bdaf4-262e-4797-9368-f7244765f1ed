# WechatSI 语音识别实现指南

## 概述

已经按照你提供的 WechatSI 用法重新实现了语音识别功能。WechatSI 是微信官方提供的同声传译插件，提供了录音识别一体化的功能。

## 实现方式

### 1. 插件配置

在 `app.json` 中配置 WechatSI 插件：

```json
{
  "plugins": {
    "WechatSI": {
      "version": "0.0.7",
      "provider": "wx069ba97219f66d99"
    }
  }
}
```

### 2. 引入插件

在 `voice-utils.js` 中引入插件：

```javascript
// 引入 WechatSI 插件
let WechatSI;
try {
  WechatSI = requirePlugin("WechatSI");
} catch (e) {
  console.log('WechatSI 插件未配置或不可用:', e);
}
```

### 3. 核心实现

按照你提供的用法实现录音识别：

```javascript
// 初始化 WechatSI 录音识别管理器
initRecordRecognitionManager() {
  if (!WechatSI) {
    return null;
  }
  
  try {
    this.recordRecognitionManager = WechatSI.getRecordRecognitionManager();
    return this.recordRecognitionManager;
  } catch (error) {
    console.log('初始化失败:', error);
    return null;
  }
}

// 使用 WechatSI 开始录音识别
startRecordWithRecognition(options = {}) {
  return new Promise((resolve, reject) => {
    const manager = this.initRecordRecognitionManager();
    
    if (!manager) {
      // 降级到普通录音
      return this.startRecord(options);
    }

    const recordOptions = {
      duration: 60000, // 最长60秒
      lang: 'zh_CN',   // 中文识别
      ...options
    };

    // 设置回调
    manager.onStart = (res) => {
      console.log("成功开始录音识别", res);
      this.isRecording = true;
      this.recordStartTime = Date.now();
      resolve({
        success: true,
        message: '开始录音识别',
        useWechatSI: true
      });
    };

    manager.onStop = (res) => {
      console.log("record file path", res.tempFilePath);
      console.log("result", res.result);
      this.isRecording = false;
      
      // 直接返回识别结果
      if (res.result && res.result.length > 0) {
        this.onRecognitionComplete && this.onRecognitionComplete({
          text: res.result,
          confidence: 0.9,
          tempFilePath: res.tempFilePath,
          provider: 'WechatSI'
        });
      } else {
        this.onRecognitionError && this.onRecognitionError(new Error('语音识别无结果'));
      }
    };

    manager.onError = (res) => {
      console.error("error msg", res.msg);
      this.isRecording = false;
      this.onRecognitionError && this.onRecognitionError(new Error(res.msg || '录音识别失败'));
    };

    // 开始录音识别
    manager.start(recordOptions);
  });
}
```

## 页面集成

### 1. 设置回调

在页面的 `startRecording` 方法中设置回调：

```javascript
startRecording() {
  // 设置 WechatSI 回调
  voiceUtils.onRecognitionComplete = (result) => {
    console.log('识别完成:', result.text);
    
    // 重置录音状态
    this.setData({
      isRecording: false,
      isLongPressing: false,
      hasStartedLongPress: false
    });
    
    // 直接添加用户消息（已经是识别后的文字）
    this.addMessage({
      type: 'user',
      content: result.text,
      timestamp: new Date().toLocaleString()
    });

    // 模拟AI回复
    this.simulateAIResponse(result.text);
  };

  voiceUtils.onRecognitionError = (error) => {
    console.log('识别失败:', error);
    
    // 重置录音状态
    this.setData({
      isRecording: false,
      isLongPressing: false,
      hasStartedLongPress: false
    });
    
    // 显示错误提示
    wx.showToast({
      title: error.message || '语音识别失败',
      icon: 'none'
    });
  };

  // 使用 WechatSI 开始录音识别
  voiceUtils.startRecordWithRecognition()
    .then(() => {
      // 录音识别启动成功
      this.setData({
        isRecording: true,
        recordDuration: 0
      });
    })
    .catch((err) => {
      // 处理启动失败
    });
}
```

### 2. 停止录音

```javascript
stopRecording() {
  // 使用 WechatSI 时，停止录音识别
  voiceUtils.stopRecordWithRecognition();
  
  // 震动反馈
  wx.vibrateShort();
  
  // 注意：识别结果会通过回调返回，不需要额外调用 speechToText
}
```

## 优势对比

### Before（原生API + 分离的语音转文字）
```
1. 开始录音 → 2. 停止录音 → 3. 获取音频文件 → 4. 调用语音识别API → 5. 获取文字结果
```

**问题**：
- 需要两个独立的API调用
- 可能出现录音成功但识别失败的情况
- 代码复杂，错误处理繁琐

### After（WechatSI 一体化）
```
1. 开始录音识别 → 2. 停止录音识别 → 3. 直接获取文字结果
```

**优势**：
- 录音和识别一体化
- 微信官方插件，稳定可靠
- 代码简洁，错误处理统一
- 自动处理音频格式和网络传输

## 功能特性

### 1. 一体化录音识别
- 录音和识别在一个API中完成
- 无需手动处理音频文件
- 自动优化音频质量

### 2. 实时反馈
- `onStart`: 录音开始
- `onStop`: 录音结束并返回识别结果
- `onError`: 错误处理

### 3. 智能降级
- WechatSI 不可用时自动降级到普通录音
- 保证功能的兼容性

### 4. 配置灵活
```javascript
manager.start({
  duration: 60000,  // 录音时长
  lang: 'zh_CN'     // 识别语言
});
```

## 错误处理

### 1. 插件不可用
```javascript
if (!WechatSI) {
  console.log('WechatSI 插件不可用，降级到普通录音');
  return this.startRecord(options);
}
```

### 2. 录音识别错误
```javascript
manager.onError = (res) => {
  console.error("error msg", res.msg);
  
  let errorMessage = '录音识别失败';
  if (res.msg) {
    if (res.msg.includes('network')) {
      errorMessage = '网络连接失败';
    } else if (res.msg.includes('auth')) {
      errorMessage = '语音识别服务未授权';
    }
  }
  
  this.onRecognitionError && this.onRecognitionError(new Error(errorMessage));
};
```

## 测试验证

### 1. 功能测试
- [ ] 长按开始录音识别
- [ ] 松开停止并获取识别结果
- [ ] 识别结果正确显示在聊天中
- [ ] 错误情况正确处理

### 2. 兼容性测试
- [ ] WechatSI 可用时使用插件
- [ ] WechatSI 不可用时降级到普通录音
- [ ] 不同设备上的兼容性

### 3. 边界测试
- [ ] 录音时间过短
- [ ] 录音时间过长
- [ ] 网络断开情况
- [ ] 无声音录音

## 注意事项

### 1. 插件版本
- 当前使用版本：0.0.7
- 需要定期检查是否有新版本

### 2. 权限要求
- 仍需要录音权限 `scope.record`
- WechatSI 插件会自动处理语音识别权限

### 3. 网络要求
- 语音识别需要网络连接
- 建议在网络良好的环境下使用

### 4. 音频格式
- WechatSI 自动处理音频格式转换
- 无需手动指定录音格式

## 总结

使用 WechatSI 插件的优势：

1. ✅ **一体化解决方案** - 录音和识别在一个API中完成
2. ✅ **官方支持** - 微信官方插件，稳定可靠
3. ✅ **代码简洁** - 减少了复杂的错误处理逻辑
4. ✅ **自动优化** - 自动处理音频质量和网络传输
5. ✅ **智能降级** - 不可用时自动降级到普通录音

现在的语音识别功能更加稳定和高效，用户体验也得到了显著提升！
