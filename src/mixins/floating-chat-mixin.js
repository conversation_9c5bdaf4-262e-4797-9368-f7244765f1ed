/**
 * 悬浮窗页面混入
 * 为页面提供悬浮窗功能
 */

const floatingChatManager = require('../utils/floating-chat-manager');

const floatingChatMixin = {
  data: {
    // 悬浮窗是否可见
    floatingChatVisible: true
  },

  onLoad() {
    // 初始化悬浮窗状态
    this.initFloatingChat();
  },

  onShow() {
    // 页面显示时检查悬浮窗状态
    this.updateFloatingChatVisibility();
  },

  // 初始化悬浮窗
  initFloatingChat() {
    const status = floatingChatManager.getStatus();
    this.setData({
      floatingChatVisible: status.shouldShow
    });
  },

  // 更新悬浮窗可见性
  updateFloatingChatVisibility() {
    const status = floatingChatManager.getStatus();
    this.setData({
      floatingChatVisible: status.shouldShow
    });
  },

  // 显示悬浮窗（全局事件处理）
  showFloatingChat(data) {
    this.setData({
      floatingChatVisible: data.show
    });
  },

  // 隐藏悬浮窗（全局事件处理）
  hideFloatingChat(data) {
    this.setData({
      floatingChatVisible: data.show
    });
  },

  // 悬浮窗点击事件
  onFloatingChatTap() {
    // 跳转到语音对话页面
    wx.navigateTo({
      url: '/pages/voice-chat/index',
      fail: (err) => {
        console.error('跳转语音对话页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 位置相关方法已移除，因为悬浮窗现在固定在左边

  // 切换悬浮窗显示状态
  toggleFloatingChat() {
    floatingChatManager.toggle();
  },

  // 临时隐藏悬浮窗（比如在某些操作时）
  temporaryHideFloatingChat() {
    this.setData({
      floatingChatVisible: false
    });
  },

  // 恢复显示悬浮窗
  restoreFloatingChat() {
    const status = floatingChatManager.getStatus();
    this.setData({
      floatingChatVisible: status.shouldShow
    });
  }
};

module.exports = floatingChatMixin;
