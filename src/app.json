{"pages": ["pages/tab/one/index", "pages/tab/two/index", "pages/tab/three/index", "pages/common/webview-page/index", "pages/common/webview-expires/index", "pages/common/dmc-blank-page/index", "pages/home/<USER>/index", "pages/home/<USER>/one", "pages/home/<USER>/two", "pages/home/<USER>/three", "pages/goods/cart/index", "pages/usercenter/dashboard/index", "pages/pay/unicashier/index", "pages/goods/detail/index", "pages/goods/seckill/index", "pages/goods/search/index", "pages/goods/group/index", "pages/goods/all/index", "pages/trade/buy/index", "pages/membercard/list/index", "pages/membercard/detail/index", "pages/membercard/active/index", "pages/membercard/setting/index", "pages/membercard/level/index", "pages/membercard/result/index", "pages/paidcontent/live/index", "pages/paidcontent/column/index", "pages/paidcontent/content/index", "pages/paidcontent/list/index", "pages/paidcontent/pay/index", "pages/salesman/tutorial/index", "pages/salesman/salesman-center/index", "pages/ump/fission/index", "pages/ump/integral-store/index", "pages/ump/integral-store/coupon/index", "pages/ump/pintuan/detail/index", "pages/order/share/index", "pages/usercenter/promotion/promotion_detail", "pages/ump/pintuan/playingInstruction/playingInstruction", "pages/shop/shopnote/list/index", "pages/account/login/index", "pages/bear/index", "pages/voice-chat/index", "pages/demo-floating-chat/index"], "usingComponents": {"home-video-ad": "components/home-video-ad/index", "van-row": "@vant/weapp/row/index", "van-col": "@vant/weapp/col/index", "van-tag": "@vant/weapp/tag/index", "van-tab": "vant-weapp/dist/tab/index", "van-tabs": "vant-weapp/dist/tabs/index", "van-card": "@vant/weapp/card/index", "van-icon": "@vant/weapp/icon/index", "van-cell": "@vant/weapp/cell/index", "van-popup": "@vant/weapp/dist/popup/index", "van-field": "@vant/weapp/dist/field/index", "van-toast": "@vant/weapp/toast/index", "van-panel": "@vant/weapp/panel/index", "van-dialog": "@vant/weapp/dialog/index", "van-button": "@vant/weapp/button/index", "van-notify": "@vant/weapp/notify/index", "van-stepper": "@vant/weapp/dist/stepper/index", "van-loading": "@vant/weapp/loading/index", "van-overlay": "@vant/weapp/overlay/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-action-sheet": "@vant/weapp/action-sheet/index", "van-switch": "@vant/weapp/switch/index", "van-search": "@vant/weapp/search/index", "van-checkbox": "@vant/weapp/checkbox/index", "van-tabbar": "@vant/weapp/tabbar/index", "van-tabbar-item": "@vant/weapp/tabbar-item/index", "van-radio": "@vant/weapp/radio/index", "van-radio-group": "@vant/weapp/radio-group/index", "znb-web-view": "components/znb-web-view/index", "page-container": "pages/common/page-container/index", "inject-protocol": "shared/components/account/inject-protocol/index", "ecloud-protocol": "shared/components/account/ecloud-protocol/index", "custom-tab-bar-v2": "src/custom-tab-bar-v2/index", "cps-goods-recommend": "components/cps-goods-recommend/index", "wsc-copyright": "pages/common/page-container/components/copyright/index", "custom-sku-header-price": "shared/components/cart/custom-sku-header-price/index", "base-sku": "shared/common/components/base-sku/index", "wsc-navigator": "components/navigator/index", "zan-contact": "components/zan-contact/index", "theme-view": "shared/common/components/theme-view/index", "theme-provider": "shared/common/components/theme-provider/index", "zan-loadmore": "shared/common/components/loadmore/index", "form-view": "shared/common/components/form-view/index", "input-number": "components/showcase/components/input-number/index", "sku-row": "shared/common/components/base-sku/sku-row/index", "sku-row-item": "shared/common/components/base-sku/sku-row-item/index", "sku-row-prop-item": "shared/common/components/base-sku/sku-row-prop-item/index", "sku-actions": "shared/common/components/base-sku/sku-actions/index", "feature-sku": "shared/components/feature-sku/index", "floating-nav": "components/showcase/components/floating-nav/index", "navigation-bar": "components/showcase/components/navigation-bar/index", "showcase-official-account": "shared/components/showcase/official-account/index", "showcase-present-gift": "shared/components/showcase/present-gift/index", "user-authorize": "shared/components/account/user-authorize/index", "ecloud-authorize": "shared/components/account/ecloud/index", "showcase-hot-words": "shared/components/showcase/hot-words/index", "collect-gift": "components/ump/collect-gift/entry-in-homepage/index", "protocol": "shared/components/account/inject-protocol/index", "marketing-page-icon": "components/ump/marketing-page/icon/index", "cap-list": "shared/components/showcase/captain-components/list/index", "sc-double-eleven": "shared/components/showcase/double-eleven/index", "sc-category": "shared/components/showcase/category/index", "share-feature": "components/showcase/components/share-feature/index", "web-view-navigator": "components/web-view-navigator/index", "external-sku": "components/external-sku/index", "collect-tip": "components/collect-tip/index", "shop-pop-manager": "components/shop-pop-manager/index", "subscribe-guide": "shared/components/message/subscribe-message/index", "salesman-cube": "components/showcase/components/salesman-cube/index", "live-popup": "components/live-pop/index", "timeline-share": "components/showcase/components/timeline-share/index", "platform-coupon-pop": "components/showcase/components/platform-coupon-pop/index", "share-activity-pop": "components/showcase/components/share-feature/components/share-activity-pop/index", "lottery-code-icon": "components/showcase/components/lottery-code-icon/index", "plugin-video": "/packages/async-video/components/video/index", "global-custom-loading": "components/global-custom-loading/index", "skyline-page-container": "components/skyline-page-container/index", "floating-chat-button": "components/floating-chat-button/index"}, "subPackages": [{"root": "packages/home", "name": "首页分包", "pages": ["bear/index", "feature/index", "tab/one", "tab/two", "tab/three"]}, {"root": "packages/scan-code-buy", "name": "扫码购首页分包", "pages": ["bear/index"]}, {"root": "packages/order", "name": "下单", "pages": ["index", "paid/index", "paid-v1/index", "share-page/index"]}, {"root": "packages/order-native", "name": "原生下单", "pages": ["index", "invoice/index", "coupon/index", "idcard/index", "contact/index", "prepay-card/index", "address-list/index", "address-edit/index", "self-fetch-address/index", "self-fetch-address-city/index", "share-page/index", "address-map/index", "address-city/index", "fastbuy/index"]}, {"root": "packages/pay", "name": "支付", "pages": ["cashier-middle-page/index", "unicashier/index", "wx-request/index", "yzp/index", "wxh5-downgrade/index", "wxh5-downgrade-result/index"]}, {"root": "packages/assets", "name": "先用后付", "pages": ["prior-use/index"]}, {"root": "packages/common", "name": "通用", "pages": ["error/index", "limit-page/index", "stop-service/index", "shopping-list/entry/index", "lock/index", "out-of-service/index"]}, {"root": "packages/statcenter", "name": "数据", "pages": ["cps-goods-list/index"]}, {"root": "packages/showcase-template", "name": "微页面模板", "pages": ["index"]}, {"root": "packages/ump", "name": "营销插件", "pages": ["fission/index", "carve-coupon/index", "integral-store/index", "integral-store/coupon/index", "pintuan/detail/index", "pintuan/playing-instruction/index", "bargain-purchase/home/<USER>", "helpcut/index", "collect-gift/home/<USER>", "gift/cart/index", "gift/goods-list/index", "gift/share/index", "gift/open-gift/index", "gift/gift-list/index", "bundle-purchase/goods-list/index", "discount-packages/index", "sign-in/index", "membercard-groupon/index", "coupon-pack/fetch/index", "split-coupon-friend/index", "plusbuy/index", "new-lottery/casino/index", "new-lottery/list/index", "share-polite-landing-pages/index", "presents/index", "handsel-expand/index", "meet-reduce-goods/index", "second-half-discount/index", "periodbuy-list/index", "lottery-code/index", "in-sourcing-fission/index", "share-benefit/index", "blind-box/auth/index", "apps/cards/index", "apps/crazy/index", "apps/crazy/help/index", "apps/shake/index", "apps/zodiac/index", "welike/index", "wine-tasting/activity/index", "wine-tasting/sign-in/index", "booking/index"]}, {"root": "packages/card", "name": "会员卡", "pages": ["all/index", "list/index", "detail/index", "active/index", "setting/index", "level/index", "result/index"]}, {"root": "packages/pre-card", "name": "储值好礼卡", "pages": ["home/index", "buy-center/index", "order-buy/index", "recharge/index", "exchange/index", "success-buy/index", "success-recharge/index", "wechat-pay/index", "disable/index", "receive/index", "record/index", "agreement/index"]}, {"root": "packages/point", "name": "集点卡", "pages": ["home/index"]}, {"root": "packages/new-punch", "name": "群打卡", "pages": ["introduction/index", "task/index", "task/diary/index", "rank/index"]}, {"root": "packages/shop", "name": "店铺", "pages": ["goods/all/index", "goods/group/index", "goods/search/index", "goods/tag-list/index", "shopnote/detail/index", "shopnote/mparticle/detail/index", "shopnote/mparticle/share/index", "shopnote/list/index", "chain-store/editaddress/index", "chain-store/shopselect/index", "chain-store/storeall/index", "multi-store/index/index", "multi-store/search/index", "multi-store/select-poi/index/index", "multi-store/select-poi/address-edit/index", "info/index", "buyers-show/list/index", "search-page/index", "ump/sign-in/index", "salesman/promote/index", "levelcenter/plus/index", "levelcenter/free/index", "levelcenter/weshop-bind-member/index", "levelcenter/member-fill-auth/index"]}, {"root": "packages/shop-select", "name": "店铺选择列表分包", "pages": ["chain-store/retail-order-shop-select/index"]}, {"root": "packages/paidcontent", "name": "知识付费", "pages": ["column/index", "content/index", "list/index", "pay/index", "gift/index", "live-room/index", "live/index", "groupon/index", "rights/index", "support/index", "invite-card/index", "exam/exam-result/index", "exam/exam-detail/index", "exam/exam-question/index"]}, {"root": "packages/edu", "name": "教育", "pages": ["webview/index", "audio-playbg/index"]}, {"root": "packages/paid", "name": "支付结果", "pages": []}, {"root": "packages/trade-pay", "name": "待支付", "pages": []}, {"root": "packages/trade-buy", "name": "下单页", "pages": ["order/address-city/index", "order/address-edit/index", "order/address-map/index", "order/contact/index", "order/self-fetch-address/index", "order/self-fetch-address-city/index"]}, {"root": "packages/trade", "name": "交易", "pages": ["cart-v1/index", "cart/index", "order/buy-h5/index", "order/express/index", "order/subscription/index", "order/list/index", "order/list-native/index", "order-detail-v1/index", "order/result/index", "order/invoice/index", "order/safe/index", "order/safe/apply/index", "order/safe/express/index", "order/safe/info/index", "order/share/index", "order/unicashier-result/index", "order/selffetch-list/index", "cert/verify-ticket/index", "goods-message/index", "refund/list/index", "order/batch-refund/index", "temp-page/index"]}, {"root": "packages/user", "name": "用户", "pages": ["coupon/fetch/index", "coupon/detail/index", "coupon/list/index", "coupon/shop/index", "coupon/qrcode/index", "coupon/goods-list/index", "integral/index", "cashback/list/index", "cashback/detail/index", "task-center/index", "membercenter/setting/index", "mediator-coupon/list/index", "mediator-coupon/invalid-list/index"]}, {"root": "packages/salesman", "name": "分销员", "pages": ["promote/index", "salesman-center/index", "tutorial/index", "invite/index", "rank/index", "report/index", "task-award/index", "task-detail/index", "business-card/index", "external-card/index", "zone/editor/index", "zone/home/<USER>", "zone/material/index"]}, {"root": "packages/channel", "name": "渠道版", "pages": ["agent/index", "indentor/index"]}, {"root": "packages/guide", "name": "导购", "pages": ["promote/goods/index", "zone/material/index"]}, {"root": "packages/groupbuying", "name": "群团购", "pages": ["buyer-trade/buying/index", "buyer-trade/detail/index", "buyer-trade/pay/index", "buyer-trade/coupon/index", "my-profit/home/<USER>", "my-profit/verify/index", "my-profit/verify-list/index", "my-profit/withdraw/account-choose/index", "my-profit/withdraw/account-set/index", "my-profit/withdraw/income-detail/index", "my-profit/withdraw/withdraw-assets/index", "my-profit/withdraw/withdraw-center/index", "my-profit/withdraw/withdraw-record/index", "header-recruit/home/<USER>", "header-recruit/message/index", "header-recruit/join-fee/index", "header-recruit/result/index", "header-recruit/chain/index", "activity/list/index", "activity/detail/index", "header-order/index", "buyer-order/index", "location/index"]}, {"root": "packages/collage", "name": "拼团", "pages": ["lottery/detail/index", "lottery/result/index", "groupon/detail/index", "groupon/join-groups/index"]}, {"root": "packages/feature-video", "name": "专题视频", "pages": ["search/index"]}, {"root": "packages/group-center", "name": "团长中心", "pages": ["index"]}, {"root": "packages/member-benefit", "name": "会员权益", "pages": ["benefit/index"]}, {"root": "packages/evaluation", "name": "商品评价", "pages": ["goods/detail/index", "goods/list/index", "order/create/index", "order/detail/index", "order/review/index"]}, {"root": "packages/message", "name": "消息", "pages": ["satisfaction/index", "contact/index", "subscribe/index", "wxkf/index"]}, {"root": "packages/account", "name": "账号", "pages": ["settings/index", "bind-new/index", "to-bind/index", "bind-update/index", "login/index"]}, {"root": "packages/takeaway-search", "name": "外卖搜索", "pages": ["index"]}, {"root": "packages/benefit-card", "name": "权益卡", "pages": ["detail/index", "intro/index", "benefit-list/index", "record-list/index", "record-detail/index", "take/index", "list/index", "active/index", "level/index", "result/index", "setting/index"]}, {"root": "packages/independent/goods", "name": "极速下单", "pages": ["fastbuy/index"]}, {"root": "pages/common/blank-page", "name": "扫码落地页面", "pages": ["index"], "independent": true}, {"root": "packages/pointstore", "name": "积分商城", "pages": ["exchange-result/index", "goods-details/index", "index/index", "point-center/index", "point-details/index", "point-rule/index", "trade-details/index", "sync-wxcard/index"]}, {"root": "packages/async-video", "name": "视频插件分包", "pages": ["index"], "independent": false}, {"root": "packages/business-card", "name": "销售员名片", "pages": ["detail/index", "auth/index"]}, {"root": "packages/levelcenter", "name": "会员等级", "pages": ["free/index", "plus/index", "pay/index", "benefit/index", "offline-recruit/index"]}, {"root": "packages/member-code", "name": "会员码", "pages": ["index"]}, {"root": "packages/zan-web-view", "name": "有赞云 webview", "pages": ["index"]}, {"root": "packages/short-video", "name": "短视频", "pages": ["index"]}, {"root": "packages/weapp-live", "name": "小程序直播", "pages": ["room/index"]}, {"root": "packages/tech-support", "name": "技术支持", "pages": ["index"]}, {"root": "packages/weass", "name": "企助", "pages": ["qrcode-process/index", "redirect/index"]}, {"root": "packages/wxvideo", "name": "视频号中转页分包", "pages": ["fastbuy/index", "cps/click/index", "cps/coupon/index", "weshop/benefit-select/index"]}, {"root": "packages/trade-cart", "name": "购物车", "pages": ["cart/index"]}], "preloadRule": {"pages/common/blank-page/index": {"network": "all", "packages": ["packages/goods-core", "packages/goods-main", "packages/goods"]}, "pages/goods/cart/index": {"network": "all", "packages": ["packages/trade"]}, "pages/goods/seckill/index": {"network": "all", "packages": ["packages/trade-buy"]}, "packages/order/index": {"network": "all", "packages": ["packages/paid"]}, "packages/paid/pay-result/success/index": {"network": "all", "packages": ["packages/trade"]}, "packages/trade-buy/order/buy/index": {"network": "all", "packages": ["packages/trade-pay", "packages/trade-buy-subpage", "packages/order", "packages/paid"]}, "packages/trade/order/list-native/index": {"network": "all", "packages": ["packages/trade-pay", "packages/order", "packages/order-native"]}, "packages/usercenter/dashboard/index": {"network": "all", "packages": ["packages/trade"]}, "packages/shop/chain-store/editaddress/index": {"network": "all", "packages": ["packages/shop-select"]}, "pages/tab/one/index": {"network": "all", "packages": ["packages/trade-buy"]}, "pages/tab/two/index": {"network": "all", "packages": ["packages/trade-buy"]}, "pages/tab/three/index": {"network": "all", "packages": ["packages/trade-buy"]}, "pages/home/<USER>/one": {"network": "all", "packages": ["packages/trade-buy"]}, "pages/home/<USER>/two": {"network": "all", "packages": ["packages/trade-buy"]}, "pages/home/<USER>/three": {"network": "all", "packages": ["packages/trade-buy"]}}, "window": {"navigationBarBackgroundColor": "#ffffff", "navigationBarTitleText": " ", "navigationBarTextStyle": "black", "backgroundTextStyle": "dark", "backgroundColor": "#f9f9f9", "onReachBottomDistance": 400, "handleWebviewPreload": "auto"}, "networkTimeout": {"request": 10000}, "plugins": {"WechatSI": {"version": "0.3.6", "provider": "wx069ba97219f66d99"}}, "navigateToMiniProgramAppIdList": ["wx069ba97219f66d99"], "permission": {"scope.userLocation": {"desc": "我们将基于你的位置信息提供准确的购物服务"}, "scope.record": {"desc": "需要您的授权以便录制语音"}}, "requiredPrivateInfos": ["getLocation", "<PERSON><PERSON><PERSON><PERSON>"], "requiredBackgroundModes": ["audio"], "debug": false, "lazyCodeLoading": "requiredComponents", "entryPagePath": "pages/home/<USER>/index", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "defaultContentBox": true, "tagNameStyleIsolation": "legacy"}}}