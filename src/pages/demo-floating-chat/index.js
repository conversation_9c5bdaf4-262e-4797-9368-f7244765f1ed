// 引入悬浮窗混入
const floatingChatMixin = require('../../mixins/floating-chat-mixin');

Page({
  // 混入悬浮窗功能
  ...floatingChatMixin,

  data: {
    ...floatingChatMixin.data,
    // 页面自己的数据
    title: '悬浮窗演示页面',
    content: '这是一个演示悬浮窗功能的页面。你可以看到左边的悬浮窗按钮，点击它可以打开语音对话页面。',
    // 导航栏相关
    statusBarHeight: 0,
    navHeight: 0
  },

  onLoad(options) {
    // 初始化系统信息
    this.initSystemInfo();

    // 调用混入的onLoad方法
    if (floatingChatMixin.onLoad) {
      floatingChatMixin.onLoad.call(this, options);
    }

    console.log('演示页面加载完成');
  },

  // 初始化系统信息
  initSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight, system } = systemInfo;

    // 计算导航栏高度
    const CAPSULE_HEIGHT = 32;
    const ANDROID_MARGIN = 8;
    const IOS_MARGIN = 6;

    let navHeight = CAPSULE_HEIGHT;
    navHeight += ~system.indexOf('iOS') ? IOS_MARGIN * 2 : ANDROID_MARGIN * 2;

    this.setData({
      statusBarHeight,
      navHeight
    });
  },

  onShow() {
    // 调用混入的onShow方法
    if (floatingChatMixin.onShow) {
      floatingChatMixin.onShow.call(this);
    }
    
    console.log('演示页面显示');
  },

  // 页面自己的方法
  onToggleFloatingChat() {
    this.toggleFloatingChat();
  },

  onTemporaryHide() {
    this.temporaryHideFloatingChat();
    
    // 3秒后恢复显示
    setTimeout(() => {
      this.restoreFloatingChat();
    }, 3000);
  }
});
