// 引入悬浮窗混入
const floatingChatMixin = require('../../mixins/floating-chat-mixin');

Page({
  // 混入悬浮窗功能
  ...floatingChatMixin,

  data: {
    ...floatingChatMixin.data,
    // 页面自己的数据
    title: '悬浮窗演示页面',
    content: '这是一个演示悬浮窗功能的页面。你可以看到右下角的悬浮窗按钮，点击它可以打开语音对话页面。'
  },

  onLoad(options) {
    // 调用混入的onLoad方法
    if (floatingChatMixin.onLoad) {
      floatingChatMixin.onLoad.call(this, options);
    }
    
    console.log('演示页面加载完成');
  },

  onShow() {
    // 调用混入的onShow方法
    if (floatingChatMixin.onShow) {
      floatingChatMixin.onShow.call(this);
    }
    
    console.log('演示页面显示');
  },

  // 页面自己的方法
  onToggleFloatingChat() {
    this.toggleFloatingChat();
  },

  onTemporaryHide() {
    this.temporaryHideFloatingChat();
    
    // 3秒后恢复显示
    setTimeout(() => {
      this.restoreFloatingChat();
    }, 3000);
  }
});
