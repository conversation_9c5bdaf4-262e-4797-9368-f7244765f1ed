.demo-container {
  padding: 20px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.demo-content {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.demo-text {
  font-size: 16px;
  line-height: 1.6;
  color: #666;
  display: block;
  margin-bottom: 30px;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 30px;
}

.demo-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  text-align: center;
}

.demo-btn:active {
  opacity: 0.8;
}

.navigator-btn {
  background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-info {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.info-title {
  font-size: 14px;
  color: #666;
  font-weight: bold;
}

.info-value {
  font-size: 14px;
  color: #333;
  margin-left: 8px;
}
