<view class="demo-container">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="height: {{navHeight}}px;">
    <view class="nav-center">
      <text class="nav-title">{{title}}</text>
    </view>
  </view>
  
  <view class="demo-content">
    <text class="demo-text">{{content}}</text>
    
    <view class="demo-buttons">
      <button class="demo-btn" bindtap="onToggleFloatingChat">
        切换悬浮窗显示状态
      </button>
      
      <button class="demo-btn" bindtap="onTemporaryHide">
        临时隐藏悬浮窗（3秒后恢复）
      </button>
      
      <navigator url="/pages/voice-chat/index" class="demo-btn navigator-btn">
        直接跳转到语音对话页面
      </navigator>
    </view>
    
    <view class="demo-info">
      <text class="info-title">悬浮窗状态：</text>
      <text class="info-value">{{floatingChatVisible ? '显示' : '隐藏'}}</text>
    </view>
  </view>
</view>

<!-- 悬浮窗聊天按钮 - 固定在左边 -->
<floating-chat-button
  wx:if="{{floatingChatVisible}}"
  show="{{floatingChatVisible}}"
  bindtap="onFloatingChatTap"
/>
