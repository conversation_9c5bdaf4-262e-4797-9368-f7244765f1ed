<!-- pages/tab/one/index.wxml -->
<feature wx:if="{{ attachedPageId === 'feature' }}" bind:attached="onTabPageAttached" id="feature" />
<cart wx:if="{{ attachedPageId === 'cart' }}" id="cart" />
<retail-shelf wx:if="{{ attachedPageId === 'retail-shelf' }}" id="retail-shelf" />
<member-code wx:if="{{ attachedPageId === 'member-code' }}" id="member-code" bind:attached="onTabPageAttached" />
<order-list wx:if="{{ attachedPageId === 'order-list' }}" id="order-list" bind:attached="onTabPageAttached"/>
<level-center-free wx:if="{{ attachedPageId === 'level-center-free' }}" id="level-center-free" bind:attached="onTabPageAttached"/>
<level-center-plus wx:if="{{ attachedPageId === 'level-center-plus' }}" id="level-center-plus" bind:attached="onTabPageAttached"/>
<all-goods-list wx:if="{{ attachedPageId === 'all-goods-list' }}" id="all-goods-list" bind:attached="onTabPageAttached"/>
<van-toast id="van-toast" />
<van-notify id="van-notify" />
<!-- 📢注意：所有接入导航的页面，在tab点击时都会展示这个loading，loading消失逻辑需要自行去页面处理，不处理默认5s消失 -->
<!-- 具体页面根据页面开始渲染的点位主动触发：app.trigger('first-render', '业务场景标识符（暂时没用到，后续埋点可以用）'); -->
<global-custom-loading  show="{{ !pageLoaded }}" />
<!-- 先把高度撑起来不然底部导航会跳动 -->
<view wx:if="{{!pageLoaded}}" style="width: 100%;height: 2000px;"></view>

<!-- 悬浮窗聊天按钮 -->
<floating-chat-button
  wx:if="{{floatingChatVisible}}"
  show="{{floatingChatVisible}}"
  position="{{floatingChatPosition}}"
  bindtap="onFloatingChatTap"
/>