const voiceUtils = require('./voice-utils');
const log = console.log;

Page({
  data: {
    // 聊天消息列表
    messageList: [],
    // 输入框内容
    inputValue: '',
    // 输入模式：'voice' 语音输入，'text' 文字输入
    inputMode: 'voice',
    // 是否应该聚焦输入框
    shouldFocus: false,
    // 是否正在录音
    isRecording: false,
    // 当前录音文件路径
    currentRecordPath: '',
    // 录音时长
    recordDuration: 0,
    // 录音定时器
    recordTimer: null,
    // 是否已经开始长按
    hasStartedLongPress: false,
    // 滚动到底部标识
    scrollToBottom: false,
    // 当前播放的语音消息ID
    currentPlayingId: null,
    // 长按开始时间
    longPressStartTime: 0,
    // 是否是长按状态
    isLongPressing: false,
    // 导航栏相关
    statusBarHeight: 0,
    navHeight: 0,
    totalNavHeight: 0,
    // 安全区域
    safeAreaBottom: 0,
    // 胶囊按钮信息
    capsuleInfo: {},
    // 键盘高度
    keyboardHeight: 0,

    // 商品卡片相关
    showProductCard: false,
    currentProduct: {},
    productCardType: 'purchase', // purchase 或 order

    // 收银台相关
    showCashier: false,
    currentOrder: {},

    // WechatSI 插件状态
    wechatSIAvailable: false
  },

  onLoad() {
    this.initSystemInfo();
    this.startRecordTimer();
    this.initKeyboardListener();
    // 添加欢迎语
    this.addWelcomeMessage();

    // 诊断 WechatSI 插件可用性
    this.diagnoseWechatSIPlugin();
  },

  // 初始化系统信息
  initSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight, safeArea } = systemInfo;

    // 获取胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算导航栏高度：胶囊按钮高度 + 上下边距
    const navHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
    const totalNavHeight = statusBarHeight + navHeight;

    // 计算安全区域
    const safeAreaBottom = systemInfo.screenHeight - safeArea.bottom;

    // 胶囊按钮信息
    const capsuleInfo = {
      width: menuButtonInfo.width,
      height: menuButtonInfo.height,
      top: menuButtonInfo.top,
      right: menuButtonInfo.right,
      bottom: menuButtonInfo.bottom,
      left: menuButtonInfo.left
    };

    this.setData({
      statusBarHeight,
      navHeight,
      totalNavHeight,
      safeAreaBottom,
      capsuleInfo
    });
  },

  // 初始化键盘监听
  initKeyboardListener() {
    // 监听键盘弹起
    wx.onKeyboardHeightChange((res) => {
      log('键盘高度变化:', res.height);
      this.setData({
        keyboardHeight: res.height
      });
    });
  },

  onUnload() {
    // 清理资源
    voiceUtils.destroy();
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
    // 移除键盘监听
    wx.offKeyboardHeightChange();
  },

  // 开始录音计时器（用于显示录音时长）
  startRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    this.data.recordTimer = setInterval(() => {
      if (this.data.isRecording) {
        const duration = voiceUtils.getRecordDuration();
        this.setData({
          recordDuration: duration,
        });
      }
    }, 100);
  },

  // 历史消息加载和保存功能已移除，每次进入都是新的对话

  // 添加欢迎语
  addWelcomeMessage() {
    const welcomeMessage = {
      id: Date.now(),
      type: 'welcome',
      content: {
        title: '🐲🍾🍭🎩，晚上好',
        subtitle: '瑞幸首个AI智能体（1.0版）上线，动动嘴就能点咖啡啦，快说说你想喝什么吧，我来安排~',
        suggestions: [
          '上一次买的快用完了，再来一单吧！',
          '最近有什么新品吗？帮我推荐一下',
          '你们店目前有什么优惠活动呢？'
        ]
      },
      timestamp: this.formatTime(),
      isUser: false,
    };

    this.setData({
      messageList: [welcomeMessage],
    });

    this.scrollToBottomLater();
  },

  // 格式化时间显示
  formatTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 添加文本消息
  addTextMessage(content) {
    const message = {
      id: Date.now(),
      type: 'text',
      content: content,
      timestamp: this.formatTime(),
      isUser: true,
    };

    this.setData({
      messageList: [...this.data.messageList, message],
    });

    // 不再保存消息到本地存储
    this.scrollToBottomLater();

    // 模拟AI回复
    this.simulateAIReply(content);
  },

  // 添加消息（通用方法）
  addMessage(messageData) {
    const message = {
      id: Date.now(),
      type: messageData.type || 'text',
      content: messageData.content,
      timestamp: messageData.timestamp || new Date().toLocaleString(),
      isUser: messageData.type === 'user',
    };

    this.setData({
      messageList: [...this.data.messageList, message],
    });

    this.scrollToBottomLater();
  },

  // 注释：getDate和addVoiceMessage方法已移除，因为现在直接显示语音识别的文字结果

  // 模拟AI回复
  simulateAIReply(userMessage) {
    setTimeout(() => {
      const aiMessage = {
        id: Date.now(),
        type: 'text',
        content: `我收到了您的消息："${userMessage}"，这是一个模拟回复。`,
        timestamp: this.formatTime(),
        isUser: false,
      };

      this.setData({
        messageList: [...this.data.messageList, aiMessage],
      });

      // 不再保存消息到本地存储
      this.scrollToBottomLater();
    }, 1000);
  },

  // 新的AI回复方法（用于语音识别后的回复）
  simulateAIResponse(userMessage) {
    setTimeout(() => {
      // 普通回复
      this.addMessage({
        type: 'bot',
        content: `我收到了您的消息："${userMessage}"，这是一个模拟回复。`,
        timestamp: new Date().toLocaleString()
      });
    }, 1000);
  },

  // 判断是否是购买相关消息
  isPurchaseMessage(message) {
    const purchaseKeywords = [
      '上一次买的快用完了',
      '再来一单',
      '再买一次',
      '重复购买',
      '老样子',
      '还是上次那个',
      '再来一份'
    ];

    return purchaseKeywords.some(keyword => message.includes(keyword));
  },

  // 处理购买流程
  handlePurchaseFlow(userMessage) {
    // 添加AI回复
    this.addMessage({
      type: 'bot',
      content: '好的，我为您找到了上次购买的商品，请确认订单信息：',
      timestamp: new Date().toLocaleString()
    });

    // 延迟显示商品卡片
    setTimeout(() => {
      const mockProduct = {
        storeName: '蜜雪冰城（凤新南路店）',
        name: '冰鲜柠檬水',
        image: '/images/lemon-water.jpg',
        calories: '180kcal',
        specs: '规格：少冰 五分糖',
        quantity: 1,
        hasAudio: true
      };

      this.showProductCardWithData(mockProduct, 'purchase');
    }, 1500);
  },

  // 语音转文字
  speechToText(filePath) {
    // 显示转换中的提示
    wx.showLoading({
      title: '语音识别中...',
      mask: true
    });

    voiceUtils
      .speechToText(filePath)
      .then((result) => {
        wx.hideLoading();

        if (result.success && result.text) {
          // 直接添加为普通文本消息，不显示语音转文字标签
          const textMessage = {
            id: Date.now(),
            type: 'text',
            content: result.text,
            timestamp: this.formatTime(),
            isUser: true,
            // 移除语音转文字相关的标识
            // isVoiceTranscript: true,
            // confidence: result.confidence,
            // isFallback: result.isFallback || false
          };

          this.setData({
            messageList: [...this.data.messageList, textMessage],
          });

          // 不再保存消息到本地存储
          this.scrollToBottomLater();

          // 显示识别成功提示
          const successTitle = result.isFallback ? '语音识别完成' : '语音识别完成';
          wx.showToast({
            title: successTitle,
            icon: 'success',
            duration: 1000
          });

          // AI回复
          setTimeout(() => {
            this.simulateAIReply(result.text);
          }, 500);
        } else {
          // 识别结果为空
          wx.showToast({
            title: '未识别到语音内容',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch((err) => {
        wx.hideLoading();
        log('语音转文字失败:', err);

        // 显示具体的错误信息
        wx.showModal({
          title: '语音识别失败',
          content: err.message || '请检查网络连接后重试',
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  // 滚动到底部
  scrollToBottomLater() {
    this.setData({
      scrollToBottom: true,
    });

    setTimeout(() => {
      this.setData({
        scrollToBottom: false,
      });
    }, 100);
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value,
    });
  },

  // 输入框聚焦
  onInputFocus() {
    log('输入框聚焦');
    // 聚焦后重置shouldFocus状态
    this.setData({
      shouldFocus: false
    });
  },

  // 输入框失焦
  onInputBlur() {
    log('输入框失焦');
  },

  // 点击欢迎语建议
  onWelcomeSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion;
    log('点击欢迎语建议:', suggestion);

    // 添加用户消息
    this.addMessage({
      type: 'user',
      content: suggestion,
      timestamp: new Date().toLocaleString()
    });

    // 检查是否是购买相关的建议
    if (this.isPurchaseMessage(suggestion)) {
      // 直接触发购买流程
      this.handlePurchaseFlow(suggestion);
    } else {
      // 普通AI回复
      this.simulateAIResponse(suggestion);
    }
  },

  // 切换输入模式
  toggleInputMode() {
    const newMode = this.data.inputMode === 'voice' ? 'text' : 'voice';
    this.setData({
      inputMode: newMode
    });

    // 切换到文字模式时，清空输入框
    if (newMode === 'text') {
      this.setData({
        inputValue: ''
      });
      // 延迟聚焦，避免立即触发键盘
      setTimeout(() => {
        this.setData({
          shouldFocus: true
        });
      }, 100);
    } else {
      // 切换到语音模式时，取消聚焦
      this.setData({
        shouldFocus: false
      });
    }
  },

  // 键盘图标点击事件（在语音模式下）
  onKeyboardIconTap(e) {
    log('键盘图标被点击');
    // 阻止事件冒泡，防止触发语音录音
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.toggleInputMode();
  },

  // 语音图标点击事件（在文字模式下）
  onVoiceIconTap(e) {
    log('语音图标被点击');
    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.toggleInputMode();
  },

  // 发送文本消息
  onSendMessage() {
    const content = this.data.inputValue.trim();
    if (!content) return;

    this.addTextMessage(content);
    this.setData({
      inputValue: '',
    });
  },

  // 长按开始 - 开始录音
  handleLongPress() {
    log('长按开始录音');

    // 防止重复触发
    if (this.data.isRecording || this.data.isLongPressing) {
      return;
    }

    // 设置长按状态
    this.setData({
      isLongPressing: true,
      hasStartedLongPress: true
    });

    // 显示开始录音的提示
    wx.showToast({
      title: '准备录音...',
      icon: 'loading',
      duration: 1000
    });

    // 震动反馈
    wx.vibrateShort();

    // 开始录音
    this.startRecording();
  },



  // 开始录音
  startRecording() {
    log('开始录音');

    // 设置 WechatSI 回调
    voiceUtils.onRecognitionComplete = (result) => {
      log('WechatSI 识别完成:', result);

      // 隐藏录音提示
      wx.hideToast();

      // 重置录音状态
      this.setData({
        isRecording: false,
        isLongPressing: false,
        hasStartedLongPress: false
      });

      // 停止录音计时器
      this.stopRecordTimer();

      // 直接添加用户消息（已经是识别后的文字）
      this.addMessage({
        type: 'user',
        content: result.text,
        timestamp: new Date().toLocaleString()
      });

      // 模拟AI回复
      this.simulateAIResponse(result.text);
    };

    voiceUtils.onRecognitionError = (error) => {
      log('WechatSI 识别失败:', error);

      // 隐藏录音提示
      wx.hideToast();

      // 重置录音状态
      this.setData({
        isRecording: false,
        isLongPressing: false,
        hasStartedLongPress: false
      });

      // 停止录音计时器
      this.stopRecordTimer();

      // 显示错误提示
      wx.showToast({
        title: error.message || '语音识别失败',
        icon: 'none',
        duration: 2000
      });
    };

    // 使用 WechatSI 开始录音识别
    voiceUtils
      .startRecordWithRecognition()
      .then(() => {
        log('录音识别启动成功');

        // 隐藏准备录音的提示
        wx.hideToast();

        this.setData({
          isRecording: true,
          recordDuration: 0,
        });

        // 震动反馈
        wx.vibrateShort();

        // 开始录音计时器
        this.startRecordTimer();

        // 显示录音提示
        wx.showToast({
          title: '正在录音，松开发送',
          icon: 'none',
          duration: 60000 // 长时间显示
        });
      })
      .catch((err) => {
        log('开始录音失败:', err);

        // 隐藏准备录音的提示
        wx.hideToast();

        // 显示详细的错误信息
        let errorTitle = '录音失败';
        let errorContent = err.message || '请检查麦克风权限和网络连接';
        let showCancel = false;
        let confirmText = '知道了';

        if (err.message.includes('隐私协议')) {
          errorTitle = '需要同意隐私协议';
          errorContent = '使用录音功能需要先同意小程序隐私协议，请重新进入小程序并同意协议';
          showCancel = true;
          confirmText = '重新进入';
        } else if (err.message.includes('权限')) {
          errorTitle = '需要录音权限';
          errorContent = '请允许小程序使用麦克风权限以使用语音功能';
        } else if (err.message.includes('网络')) {
          errorTitle = '网络连接失败';
        }

        wx.showModal({
          title: errorTitle,
          content: errorContent,
          showCancel: showCancel,
          confirmText: confirmText,
          success: (modalRes) => {
            if (modalRes.confirm && err.message.includes('隐私协议')) {
              // 重新启动小程序
              wx.reLaunch({
                url: '/pages/voice-chat/index'
              });
            }
          }
        });

        this.setData({
          isLongPressing: false
        });
      });
  },

  // 触摸结束 - 结束录音
  handleTouchEnd() {
    log('触摸结束');

    // 如果没有开始长按，直接返回
    if (!this.data.hasStartedLongPress) {
      log('没有长按，不执行录音操作');
      return;
    }

    this.setData({
      isLongPressing: false,
      hasStartedLongPress: false
    });

    // 如果没有在录音，直接返回
    if (!this.data.isRecording) {
      return;
    }

    // 隐藏录音提示
    wx.hideToast();

    // 检查录音时长，太短的录音不处理
    if (this.data.recordDuration < 1) {
      wx.showToast({
        title: '录音时间太短',
        icon: 'none'
      });

      // 停止录音但不保存
      voiceUtils.stopRecordWithRecognition();
      this.setData({
        isRecording: false
      });
      return;
    }

    // 停止录音并保存
    this.stopRecording();
  },

  // 停止录音
  stopRecording() {
    // 使用 WechatSI 时，停止录音识别
    voiceUtils.stopRecordWithRecognition();

    // 停止录音计时器
    this.stopRecordTimer();

    // 震动反馈
    wx.vibrateShort();

    // 注意：使用 WechatSI 时，识别结果会通过回调返回
    // 不需要额外调用 speechToText 方法
  },



  // 语音播放相关方法已移除，因为现在不再显示语音消息

  // 返回按钮点击
  onBackTap() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到首页
        wx.switchTab({
          url: '/pages/tab/one/index',
          fail: () => {
            wx.reLaunch({
              url: '/pages/tab/one/index'
            });
          }
        });
      }
    });
  },

  // 防拖拽功能已通过CSS实现，不再需要JavaScript方法

  // 测试录音功能已移除

  // 清空聊天记录功能已移除，每次进入都是新的对话

  // ========== WechatSI 插件诊断方法 ==========

  // 诊断 WechatSI 插件可用性
  async diagnoseWechatSIPlugin() {
    log('🔍 开始诊断 WechatSI 插件...');

    try {
      // 执行综合诊断
      const result = await voiceUtils.diagnosePlugin();

      // 显示诊断结果
      this.showDiagnosisResult(result);

      // 根据诊断结果设置状态
      this.setData({
        wechatSIAvailable: result.summary.canUse
      });

    } catch (error) {
      log('❌ 诊断过程出错:', error);

      // 显示错误信息
      wx.showModal({
        title: 'WechatSI 诊断失败',
        content: `诊断过程出现错误：${error.message}`,
        showCancel: false,
        confirmText: '知道了'
      });
    }
  },

  // 显示诊断结果
  showDiagnosisResult(result) {
    log('📊 诊断结果:', result);

    // 构建详细信息
    const details = result.checks.map(check =>
      `${check.name}: ${check.status === 'pass' ? '✅' : check.status === 'fail' ? '❌' : '⚠️'}`
    ).join('\n');

    // 显示诊断结果弹窗
    wx.showModal({
      title: 'WechatSI 插件诊断',
      content: `${result.summary.recommendation}\n\n详细检查:\n${details}`,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          // 显示详细诊断信息
          this.showDetailedDiagnosis(result);
        }
      }
    });

    // 如果插件不可用，添加提示消息
    if (!result.summary.canUse) {
      setTimeout(() => {
        this.addMessage({
          type: 'bot',
          content: `⚠️ WechatSI 语音识别插件检测异常\n\n${result.summary.recommendation}\n\n当前将使用备用的语音录制功能，但无法提供实时语音转文字服务。`,
          timestamp: new Date().toLocaleString()
        });
      }, 1000);
    } else {
      setTimeout(() => {
        this.addMessage({
          type: 'bot',
          content: '✅ WechatSI 语音识别插件工作正常，您可以使用长按录音功能进行语音交互！',
          timestamp: new Date().toLocaleString()
        });
      }, 1000);
    }
  },

  // 显示详细诊断信息
  showDetailedDiagnosis(result) {
    const detailText = JSON.stringify(result, null, 2);

    // 复制到剪贴板
    wx.setClipboardData({
      data: detailText,
      success: () => {
        wx.showToast({
          title: '诊断信息已复制',
          icon: 'success'
        });
      }
    });

    // 显示详细信息
    wx.showModal({
      title: '详细诊断信息',
      content: '诊断信息已复制到剪贴板，您可以发送给开发者进行问题排查。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  // 手动重新检测插件
  recheckWechatSIPlugin() {
    wx.showLoading({
      title: '重新检测中...'
    });

    setTimeout(() => {
      wx.hideLoading();
      this.diagnoseWechatSIPlugin();
    }, 1000);
  },

  // ========== 购买流程相关方法 ==========

  // 显示商品卡片
  showProductCardWithData(product, type = 'purchase') {
    this.setData({
      currentProduct: product,
      productCardType: type,
      showProductCard: true
    });
  },

  // 查看价格并支付
  onViewPriceAndPay(e) {
    const product = e.detail.product;
    log('查看价格并支付:', product);

    // 构建订单信息
    const orderInfo = {
      storeName: product.storeName,
      amount: '4.00',
      hasAudio: product.hasAudio,
      product: product
    };

    // 显示收银台
    this.setData({
      currentOrder: orderInfo,
      showCashier: true,
      showProductCard: false // 隐藏商品卡片
    });
  },

  // 查看订单详情
  onViewOrderDetail(e) {
    const product = e.detail.product;
    log('查看订单详情:', product);

    // 跳转到订单详情页面
    wx.navigateTo({
      url: '/pages/order/detail/index?orderId=' + (product.orderId || '12345'),
      fail: () => {
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 修改商品规格
  onModifySpecs(e) {
    const product = e.detail.product;
    log('修改商品规格:', product);

    wx.showToast({
      title: '规格修改功能开发中',
      icon: 'none'
    });
  },

  // 关闭收银台
  onCloseCashier() {
    this.setData({
      showCashier: false,
      showProductCard: true // 重新显示商品卡片
    });
  },

  // 支付成功
  onPaymentSuccess(e) {
    const { orderInfo, paymentMethod } = e.detail;
    log('支付成功:', orderInfo, paymentMethod);

    // 隐藏收银台
    this.setData({
      showCashier: false,
      showProductCard: false
    });

    // 显示支付成功提示
    wx.showToast({
      title: '支付成功',
      icon: 'success',
      duration: 2000
    });

    // 延迟显示订单确认卡片
    setTimeout(() => {
      // 构建订单商品信息
      const orderProduct = {
        ...orderInfo.product,
        orderId: 'ORDER_' + Date.now(),
        orderTime: new Date().toLocaleString(),
        paymentMethod: paymentMethod.name
      };

      // 添加机器人消息 - 订单确认
      this.addMessage({
        type: 'bot',
        content: '支付成功！您的订单已确认，以下是订单详情：',
        timestamp: new Date().toLocaleString()
      });

      // 显示订单卡片
      this.showProductCardWithData(orderProduct, 'order');
    }, 2500);
  }
});
