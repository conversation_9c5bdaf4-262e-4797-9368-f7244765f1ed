const voiceUtils = require('./voice-utils');
const log = console.log;

Page({
  data: {
    // 聊天消息列表
    messageList: [],
    // 输入框内容
    inputValue: '',
    // 输入模式：'voice' 语音输入，'text' 文字输入
    inputMode: 'voice',
    // 是否应该聚焦输入框
    shouldFocus: false,
    // 是否正在录音
    isRecording: false,
    // 当前录音文件路径
    currentRecordPath: '',
    // 录音时长
    recordDuration: 0,
    // 录音定时器
    recordTimer: null,
    // 滚动到底部标识
    scrollToBottom: false,
    // 当前播放的语音消息ID
    currentPlayingId: null,
    // 长按开始时间
    longPressStartTime: 0,
    // 是否是长按状态
    isLongPressing: false,
    // 导航栏相关
    statusBarHeight: 0,
    navHeight: 0,
    totalNavHeight: 0,
    // 安全区域
    safeAreaBottom: 0,
    // 胶囊按钮信息
    capsuleInfo: {},
    // 键盘高度
    keyboardHeight: 0
  },

  onLoad() {
    this.initSystemInfo();
    this.startRecordTimer();
    this.initKeyboardListener();
    // 添加欢迎语
    this.addWelcomeMessage();
  },

  // 初始化系统信息
  initSystemInfo() {
    const systemInfo = wx.getSystemInfoSync();
    const { statusBarHeight, safeArea } = systemInfo;

    // 获取胶囊按钮位置信息
    const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

    // 计算导航栏高度：胶囊按钮高度 + 上下边距
    const navHeight = (menuButtonInfo.top - statusBarHeight) * 2 + menuButtonInfo.height;
    const totalNavHeight = statusBarHeight + navHeight;

    // 计算安全区域
    const safeAreaBottom = systemInfo.screenHeight - safeArea.bottom;

    // 胶囊按钮信息
    const capsuleInfo = {
      width: menuButtonInfo.width,
      height: menuButtonInfo.height,
      top: menuButtonInfo.top,
      right: menuButtonInfo.right,
      bottom: menuButtonInfo.bottom,
      left: menuButtonInfo.left
    };

    this.setData({
      statusBarHeight,
      navHeight,
      totalNavHeight,
      safeAreaBottom,
      capsuleInfo
    });
  },

  // 初始化键盘监听
  initKeyboardListener() {
    // 监听键盘弹起
    wx.onKeyboardHeightChange((res) => {
      log('键盘高度变化:', res.height);
      this.setData({
        keyboardHeight: res.height
      });
    });
  },

  onUnload() {
    // 清理资源
    voiceUtils.destroy();
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
    // 移除键盘监听
    wx.offKeyboardHeightChange();
  },

  // 开始录音计时器（用于显示录音时长）
  startRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    this.data.recordTimer = setInterval(() => {
      if (this.data.isRecording) {
        const duration = voiceUtils.getRecordDuration();
        this.setData({
          recordDuration: duration,
        });
      }
    }, 100);
  },

  // 历史消息加载和保存功能已移除，每次进入都是新的对话

  // 添加欢迎语
  addWelcomeMessage() {
    const welcomeMessage = {
      id: Date.now(),
      type: 'welcome',
      content: {
        title: '洪小姐，晚上好',
        subtitle: '瑞幸首个AI智能体（1.0版）上线，动动嘴就能点咖啡啦，快说说你想喝什么吧，我来安排~',
        suggestions: [
          '老样子，再来一杯吧！',
          '最近的新品好喝吗？帮我推荐一下',
          '尝尝爆款生椰拿铁吧~'
        ]
      },
      timestamp: this.formatTime(),
      isUser: false,
    };

    this.setData({
      messageList: [welcomeMessage],
    });

    this.scrollToBottomLater();
  },

  // 格式化时间显示
  formatTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 添加文本消息
  addTextMessage(content) {
    const message = {
      id: Date.now(),
      type: 'text',
      content: content,
      timestamp: this.formatTime(),
      isUser: true,
    };

    this.setData({
      messageList: [...this.data.messageList, message],
    });

    // 不再保存消息到本地存储
    this.scrollToBottomLater();

    // 模拟AI回复
    this.simulateAIReply(content);
  },

  // 注释：getDate和addVoiceMessage方法已移除，因为现在直接显示语音识别的文字结果

  // 模拟AI回复
  simulateAIReply(userMessage) {
    setTimeout(() => {
      const aiMessage = {
        id: Date.now(),
        type: 'text',
        content: `我收到了您的消息："${userMessage}"，这是一个模拟回复。`,
        timestamp: this.formatTime(),
        isUser: false,
      };

      this.setData({
        messageList: [...this.data.messageList, aiMessage],
      });

      // 不再保存消息到本地存储
      this.scrollToBottomLater();
    }, 1000);
  },

  // 语音转文字
  voiceToText(filePath) {
    // 显示转换中的提示
    wx.showLoading({
      title: '语音识别中...',
      mask: true
    });

    voiceUtils
      .speechToText(filePath)
      .then((result) => {
        wx.hideLoading();

        if (result.success && result.text) {
          // 直接添加为普通文本消息，不显示语音转文字标签
          const textMessage = {
            id: Date.now(),
            type: 'text',
            content: result.text,
            timestamp: this.formatTime(),
            isUser: true,
            // 移除语音转文字相关的标识
            // isVoiceTranscript: true,
            // confidence: result.confidence,
            // isFallback: result.isFallback || false
          };

          this.setData({
            messageList: [...this.data.messageList, textMessage],
          });

          // 不再保存消息到本地存储
          this.scrollToBottomLater();

          // 显示识别成功提示
          const successTitle = result.isFallback ? '语音识别完成' : '语音识别完成';
          wx.showToast({
            title: successTitle,
            icon: 'success',
            duration: 1000
          });

          // AI回复
          setTimeout(() => {
            this.simulateAIReply(result.text);
          }, 500);
        } else {
          // 识别结果为空
          wx.showToast({
            title: '未识别到语音内容',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch((err) => {
        wx.hideLoading();
        log('语音转文字失败:', err);

        // 显示具体的错误信息
        wx.showModal({
          title: '语音识别失败',
          content: err.message || '请检查网络连接后重试',
          showCancel: false,
          confirmText: '知道了'
        });
      });
  },

  // 滚动到底部
  scrollToBottomLater() {
    this.setData({
      scrollToBottom: true,
    });

    setTimeout(() => {
      this.setData({
        scrollToBottom: false,
      });
    }, 100);
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value,
    });
  },

  // 输入框聚焦
  onInputFocus() {
    log('输入框聚焦');
    // 聚焦后重置shouldFocus状态
    this.setData({
      shouldFocus: false
    });
  },

  // 输入框失焦
  onInputBlur() {
    log('输入框失焦');
  },

  // 点击欢迎语建议
  onWelcomeSuggestionTap(e) {
    const suggestion = e.currentTarget.dataset.suggestion;
    log('点击欢迎语建议:', suggestion);

    // 添加用户消息
    this.addTextMessage(suggestion);
  },

  // 切换输入模式
  toggleInputMode() {
    const newMode = this.data.inputMode === 'voice' ? 'text' : 'voice';
    log('切换输入模式:', this.data.inputMode, '->', newMode);

    this.setData({
      inputMode: newMode
    });

    // 切换到文字模式时，清空输入框
    if (newMode === 'text') {
      this.setData({
        inputValue: ''
      });
      // 延迟聚焦，避免立即触发键盘
      setTimeout(() => {
        this.setData({
          shouldFocus: true
        });
      }, 100);
    } else {
      // 切换到语音模式时，取消聚焦
      this.setData({
        shouldFocus: false
      });
    }
  },

  // 键盘图标点击事件（在语音模式下）
  onKeyboardIconTap(e) {
    log('键盘图标被点击，阻止事件冒泡');
    // 阻止事件冒泡，防止触发语音录音
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.toggleInputMode();
  },

  // 语音图标点击事件（在文字模式下）
  onVoiceIconTap(e) {
    log('语音图标被点击，阻止事件冒泡');
    // 阻止事件冒泡
    if (e && e.stopPropagation) {
      e.stopPropagation();
    }
    this.toggleInputMode();
  },

  // 发送文本消息
  onSendMessage() {
    const content = this.data.inputValue.trim();
    if (!content) return;

    this.addTextMessage(content);
    this.setData({
      inputValue: '',
    });
  },

  // 触摸开始 - 直接开始录音
  onLongPressStart() {
    log('🎤 开始长按录音');

    // 测试录音器状态
    voiceUtils.testRecorderStatus();

    // 显示开始录音的提示
    wx.showToast({
      title: '准备录音...',
      icon: 'loading',
      duration: 1000
    });

    this.setData({
      isLongPressing: true
    });

    // 直接开始录音
    this.startRecording();
  },

  // 主动申请录音权限
  requestRecordPermission() {
    log('=== 主动申请录音权限 ===');

    voiceUtils
      .requestRecordPermission()
      .then(() => {
        log('✅ 权限申请成功');
        wx.showToast({
          title: '权限申请成功',
          icon: 'success',
          duration: 2000
        });
      })
      .catch((err) => {
        log('❌ 权限申请失败:', err);
        wx.showModal({
          title: '权限申请失败',
          content: err.message,
          showCancel: false
        });
      });
  },

  // 测试录音器API
  testRecorderAPI() {
    log('=== 测试录音器API ===');
    voiceUtils.checkRecorderAPI();

    wx.showModal({
      title: 'API检查完成',
      content: '请查看控制台日志',
      showCancel: false
    });
  },

  // 测试简化录音
  testSimpleRecording() {
    log('=== 测试简化录音 ===');

    voiceUtils
      .startRecordSimple()
      .then(() => {
        log('✅ 简化录音启动成功');

        this.setData({
          isRecording: true,
          recordDuration: 0,
        });

        wx.showToast({
          title: '简化录音成功',
          icon: 'success',
          duration: 2000
        });
      })
      .catch((err) => {
        log('❌ 简化录音失败:', err);
        wx.showModal({
          title: '简化录音失败',
          content: err.message,
          showCancel: false
        });
      });
  },

  // 开始录音
  startRecording() {
    log('=== 页面开始录音流程 ===');

    voiceUtils
      .startRecord()
      .then(() => {
        log('✅ 录音启动成功');

        // 隐藏准备录音的提示
        wx.hideToast();

        this.setData({
          isRecording: true,
          recordDuration: 0,
        });

        // 震动反馈
        wx.vibrateShort();

        // 显示录音提示
        wx.showToast({
          title: '正在录音，松开发送',
          icon: 'none',
          duration: 60000 // 长时间显示
        });
      })
      .catch((err) => {
        log('❌ 开始录音失败:', err);
        log('错误详情:', err.message);

        // 隐藏准备录音的提示
        wx.hideToast();

        // 显示详细的错误信息
        let errorTitle = '录音失败';
        if (err.message.includes('权限')) {
          errorTitle = '需要录音权限';
        } else if (err.message.includes('网络')) {
          errorTitle = '网络连接失败';
        } else if (err.message.includes('超时')) {
          errorTitle = '权限申请超时';
        }

        wx.showModal({
          title: errorTitle,
          content: err.message || '请检查麦克风权限和网络连接',
          showCancel: false,
          confirmText: '知道了'
        });

        this.setData({
          isLongPressing: false
        });
      });
  },

  // 触摸结束 - 结束录音
  onLongPressEnd() {
    log('触摸结束');

    this.setData({
      isLongPressing: false
    });

    // 如果没有在录音，直接返回
    if (!this.data.isRecording) {
      return;
    }

    // 隐藏录音提示
    wx.hideToast();

    // 检查录音时长，太短的录音不处理
    if (this.data.recordDuration < 1) {
      wx.showToast({
        title: '录音时间太短',
        icon: 'none'
      });

      // 停止录音但不保存
      voiceUtils.stopRecord().catch(() => {});
      this.setData({
        isRecording: false
      });
      return;
    }

    // 停止录音并保存
    this.stopRecording();
  },

  // 停止录音
  stopRecording() {
    voiceUtils
      .stopRecord()
      .then((result) => {
        this.setData({
          isRecording: false,
          currentRecordPath: result.tempFilePath,
        });

        // 震动反馈
        wx.vibrateShort();

        // 直接进行语音识别，不添加语音消息
        this.voiceToText(result.tempFilePath);
      })
      .catch((err) => {
        log('停止录音失败:', err);
        this.setData({
          isRecording: false,
        });
        wx.showToast({
          title: '录音失败',
          icon: 'none',
        });
      });
  },

  // 触摸取消 - 取消录音
  onLongPressCancel() {
    log('触摸取消，录音被取消');

    this.setData({
      isLongPressing: false
    });

    if (this.data.isRecording) {
      // 隐藏录音提示
      wx.hideToast();

      // 停止录音但不保存
      voiceUtils.stopRecord().catch(() => {});
      this.setData({
        isRecording: false
      });

      wx.showToast({
        title: '录音已取消',
        icon: 'none'
      });
    }
  },

  // 语音播放相关方法已移除，因为现在不再显示语音消息

  // 返回按钮点击
  onBackTap() {
    wx.navigateBack({
      fail: () => {
        // 如果没有上一页，则跳转到首页
        wx.switchTab({
          url: '/pages/tab/one/index',
          fail: () => {
            wx.reLaunch({
              url: '/pages/tab/one/index'
            });
          }
        });
      }
    });
  },

  // 防拖拽功能已通过CSS实现，不再需要JavaScript方法

  // 测试录音功能已移除

  // 清空聊天记录功能已移除，每次进入都是新的对话
});
