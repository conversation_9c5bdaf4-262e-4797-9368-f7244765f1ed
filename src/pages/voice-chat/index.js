const voiceUtils = require('./voice-utils');

Page({
  data: {
    // 聊天消息列表
    messageList: [],
    // 输入框内容
    inputValue: '',
    // 是否正在录音
    isRecording: false,
    // 当前录音文件路径
    currentRecordPath: '',
    // 录音时长
    recordDuration: 0,
    // 录音定时器
    recordTimer: null,
    // 滚动到底部标识
    scrollToBottom: false,
    // 当前播放的语音消息ID
    currentPlayingId: null,
  },

  onLoad() {
    this.loadHistoryMessages();
    this.startRecordTimer();
  },

  onUnload() {
    // 清理资源
    voiceUtils.destroy();
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }
  },

  // 开始录音计时器（用于显示录音时长）
  startRecordTimer() {
    if (this.data.recordTimer) {
      clearInterval(this.data.recordTimer);
    }

    this.data.recordTimer = setInterval(() => {
      if (this.data.isRecording) {
        const duration = voiceUtils.getRecordDuration();
        this.setData({
          recordDuration: duration,
        });
      }
    }, 100);
  },

  // 加载历史消息
  loadHistoryMessages() {
    // 这里可以从本地存储或服务器加载历史消息
    const historyMessages = wx.getStorageSync('chatMessages') || [];
    this.setData({
      messageList: historyMessages,
    });
    this.scrollToBottomLater();
  },

  // 保存消息到本地存储
  saveMessages() {
    wx.setStorageSync('chatMessages', this.data.messageList);
  },

  // 添加文本消息
  addTextMessage(content) {
    const message = {
      id: Date.now(),
      type: 'text',
      content: content,
      timestamp: new Date().getTime(),
      isUser: true,
    };

    this.setData({
      messageList: [...this.data.messageList, message],
    });

    this.saveMessages();
    this.scrollToBottomLater();

    // 模拟AI回复
    this.simulateAIReply(content);
  },

  // 添加语音消息
  addVoiceMessage(filePath, duration) {
    const timestamp = new Date().getTime();
    const formattedDate = new Date(timestamp)
      .toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      })
      .replace(/\//g, '-');
    const message = {
      id: Date.now(),
      type: 'voice',
      content: filePath,
      duration: duration,
      timestamp: ,
      isUser: true,
      isPlaying: false,
    };

    this.setData({
      messageList: [...this.data.messageList, message],
    });

    this.saveMessages();
    this.scrollToBottomLater();

    // 语音转文字和AI回复
    this.voiceToText(filePath);
  },

  // 模拟AI回复
  simulateAIReply(userMessage) {
    setTimeout(() => {
      const aiMessage = {
        id: Date.now(),
        type: 'text',
        content: `我收到了您的消息："${userMessage}"，这是一个模拟回复。`,
        timestamp: new Date().getTime(),
        isUser: false,
      };

      this.setData({
        messageList: [...this.data.messageList, aiMessage],
      });

      this.saveMessages();
      this.scrollToBottomLater();
    }, 1000);
  },

  // 语音转文字
  voiceToText(filePath) {
    // 显示转换中的提示
    wx.showLoading({
      title: '语音识别中...',
    });

    voiceUtils
      .speechToText(filePath)
      .then((result) => {
        wx.hideLoading();

        if (result.success) {
          const textMessage = {
            id: Date.now(),
            type: 'text',
            content: result.text,
            timestamp: new Date().getTime(),
            isUser: true,
            isVoiceTranscript: true,
            confidence: result.confidence,
          };

          // 更新消息列表，在语音消息后添加转文字结果
          const messageList = [...this.data.messageList];
          messageList.push(textMessage);

          this.setData({
            messageList,
          });

          this.saveMessages();
          this.scrollToBottomLater();

          // AI回复
          this.simulateAIReply(result.text);
        } else {
          wx.showToast({
            title: '语音识别失败',
            icon: 'none',
          });
        }
      })
      .catch((err) => {
        wx.hideLoading();
        console.error('语音转文字失败:', err);
        wx.showToast({
          title: '语音识别失败',
          icon: 'none',
        });
      });
  },

  // 滚动到底部
  scrollToBottomLater() {
    this.setData({
      scrollToBottom: true,
    });

    setTimeout(() => {
      this.setData({
        scrollToBottom: false,
      });
    }, 100);
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value,
    });
  },

  // 发送文本消息
  onSendMessage() {
    const content = this.data.inputValue.trim();
    if (!content) return;

    this.addTextMessage(content);
    this.setData({
      inputValue: '',
    });
  },

  // 开始录音
  onStartRecord() {
    voiceUtils
      .startRecord()
      .then(() => {
        this.setData({
          isRecording: true,
          recordDuration: 0,
        });
      })
      .catch((err) => {
        console.error('开始录音失败:', err);
        wx.showToast({
          title: err.message || '录音失败',
          icon: 'none',
        });
      });
  },

  // 停止录音
  onStopRecord() {
    if (!this.data.isRecording) return;

    voiceUtils
      .stopRecord()
      .then((result) => {
        this.setData({
          isRecording: false,
          currentRecordPath: result.tempFilePath,
        });

        // 添加语音消息到聊天列表
        this.addVoiceMessage(result.tempFilePath, result.duration);
      })
      .catch((err) => {
        console.error('停止录音失败:', err);
        this.setData({
          isRecording: false,
        });
        wx.showToast({
          title: '录音失败',
          icon: 'none',
        });
      });
  },

  // 播放语音消息
  onPlayVoice(e) {
    const { index } = e.currentTarget.dataset;
    const message = this.data.messageList[index];

    if (message.type !== 'voice') return;

    // 如果当前有其他语音在播放，先停止
    if (
      this.data.currentPlayingId &&
      this.data.currentPlayingId !== message.id
    ) {
      voiceUtils.stopAudio();
      this.updateMessagePlayingState(this.data.currentPlayingId, false);
    }

    const isCurrentlyPlaying = message.isPlaying;

    if (isCurrentlyPlaying) {
      // 停止播放
      voiceUtils.stopAudio();
      this.updateMessagePlayingState(message.id, false);
      this.setData({
        currentPlayingId: null,
      });
    } else {
      // 开始播放
      voiceUtils
        .playAudio(message.content)
        .then(() => {
          this.updateMessagePlayingState(message.id, true);
          this.setData({
            currentPlayingId: message.id,
          });
        })
        .catch((err) => {
          console.error('播放失败:', err);
          wx.showToast({
            title: '播放失败',
            icon: 'none',
          });
        });

      // 监听播放结束
      voiceUtils.innerAudioContext.onEnded(() => {
        this.updateMessagePlayingState(message.id, false);
        this.setData({
          currentPlayingId: null,
        });
      });
    }
  },

  // 更新消息播放状态
  updateMessagePlayingState(messageId, isPlaying) {
    const messageList = [...this.data.messageList];
    const messageIndex = messageList.findIndex((msg) => msg.id === messageId);

    if (messageIndex !== -1) {
      messageList[messageIndex].isPlaying = isPlaying;
      this.setData({
        messageList,
      });
    }
  },

  // 清空聊天记录
  onClearMessages() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有聊天记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            messageList: [],
          });
          wx.removeStorageSync('chatMessages');
        }
      },
    });
  },
});
