// 语音识别配置文件

const speechConfig = {
  // 语音识别服务提供商
  provider: 'mock', // 可选值: 'baidu', 'wxcloud', 'mock'
  
  // 百度语音识别配置
  baidu: {
    // 服务器API地址（需要自己搭建服务器中转）
    apiUrl: 'https://your-server.com/api/speech-to-text',
    // 超时时间
    timeout: 10000
  },
  
  // 微信云开发配置
  wxcloud: {
    // 云函数名称
    functionName: 'speechToText',
    // 超时时间
    timeout: 10000
  },
  
  // 模拟配置
  mock: {
    // 模拟延迟时间
    delay: 1500,
    // 模拟文本库
    texts: [
      '你好，我想了解一下产品信息',
      '请问有什么优惠活动吗？',
      '我要预订明天的服务',
      '谢谢你的帮助',
      '再见',
      '我想咨询一下价格',
      '这个产品怎么样？',
      '有没有其他颜色的？',
      '什么时候能发货？',
      '支持退换货吗？'
    ]
  }
};

module.exports = speechConfig;
