<view class="voice-chat-container">
  <!-- 状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 自定义导航栏 -->
  <view class="custom-nav" style="height: {{navHeight}}px;">
    <view class="nav-left">
      <view class="nav-back-btn" bindtap="onBackTap">
        <text class="nav-back-icon">‹</text>
      </view>
    </view>
    <view class="nav-center">
      <text class="nav-title">语音助手</text>
    </view>
    <view class="nav-right" style="width: {{capsuleInfo.width}}px; margin-right: 16px;">
      <!-- 清空按钮已移除 -->
    </view>
  </view>

  <!-- 聊天消息区域 -->
  <scroll-view
    class="chat-messages"
    scroll-y="true"
    scroll-into-view="{{scrollToBottom ? 'bottom-anchor' : ''}}"
    scroll-with-animation="true"
  >
    <view class="messages-container">
      <block wx:for="{{messageList}}" wx:key="id">
        <view class="message-item {{item.isUser ? 'user-message' : 'ai-message'}}">
          <!-- 时间戳 -->
          <view class="message-time">
            <text class="time-text">{{item.timestamp}}</text>
          </view>

          <!-- 用户消息 -->
          <view wx:if="{{item.isUser}}" class="user-message-content">
            <!-- 文本消息 -->
            <view wx:if="{{item.type === 'text'}}" class="text-message {{item.isVoiceTranscript ? 'voice-transcript' : ''}}">
              <text class="message-text">{{item.content}}</text>
              <view wx:if="{{item.isVoiceTranscript}}" class="transcript-tag">
                <text class="tag-text">{{item.isFallback ? '模拟识别' : '语音转文字'}}</text>
                <view wx:if="{{item.confidence}}" class="confidence-indicator">
                  <text class="confidence-text">{{Math.round(item.confidence * 100)}}%</text>
                </view>
              </view>
            </view>
            
            <!-- 语音消息已移除，现在直接显示识别的文字 -->
            
            <view class="message-avatar user-avatar">
              <text class="avatar-text">我</text>
            </view>
          </view>

          <!-- AI消息 -->
          <view wx:else class="ai-message-content">
            <view class="message-avatar ai-avatar">
              <text class="avatar-text">AI</text>
            </view>

            <!-- 欢迎语消息 -->
            <view wx:if="{{item.type === 'welcome'}}" class="welcome-message">
              <view class="welcome-header">
                <text class="welcome-title">{{item.content.title}}</text>
                <text class="welcome-subtitle">{{item.content.subtitle}}</text>
              </view>

              <view class="welcome-suggestions">
                <view
                  wx:for="{{item.content.suggestions}}"
                  wx:key="index"
                  class="suggestion-item"
                  data-suggestion="{{item}}"
                  bindtap="onWelcomeSuggestionTap"
                >
                  <text class="suggestion-text">{{item}}</text>
                  <view class="suggestion-arrow">
                    <text class="arrow-icon">→</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 普通文本消息 -->
            <view wx:else class="text-message">
              <text class="message-text">{{item.content}}</text>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 空状态 -->
      <view wx:if="{{messageList.length === 0}}" class="empty-state">
        <view class="empty-icon">💬</view>
        <text class="empty-text">开始你的语音对话吧</text>
      </view>
      
      <!-- 滚动锚点 -->
      <view id="bottom-anchor"></view>
    </view>
  </scroll-view>

  <!-- 底部输入区域 -->
  <view class="chat-input-area" style="padding-bottom: {{keyboardHeight > 0 ? keyboardHeight : safeAreaBottom}}px;">
    <!-- 录音状态显示 -->
    <view wx:if="{{isRecording}}" class="recording-status">
      <view class="recording-animation">
        <view class="recording-dot"></view>
        <view class="recording-dot"></view>
        <view class="recording-dot"></view>
      </view>
      <text class="recording-text">正在录音... {{recordDuration}}s</text>
      <text class="recording-tip">松开发送</text>
    </view>



    <!-- 输入区域 -->
    <view class="input-container">
      <!-- 语音输入模式 -->
      <view wx:if="{{inputMode === 'voice'}}" class="voice-input-mode">
        <view class="voice-input-container">
          <!-- 键盘图标（移到语音按钮外部） -->
          <view class="keyboard-icon-external" catchtap="onKeyboardIconTap" data-action="toggle">
            <text class="icon">⌨️</text>
          </view>

          <!-- 语音按钮 -->
          <view
            class="voice-btn {{isRecording ? 'recording' : ''}}"
            bindlongpress="handleLongPress"
            bindtouchend="handleTouchEnd"
          >
            <!-- 按钮状态显示 -->
            <view wx:if="{{!isRecording}}" class="voice-btn-normal">
              <text class="voice-btn-text">按住 说话</text>
            </view>

            <!-- 录音中状态 -->
            <view wx:if="{{isRecording}}" class="voice-btn-recording">
              <view class="recording-animation">
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
                <view class="wave-bar"></view>
              </view>
              <text class="recording-tip">松开发送</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 文字输入模式 -->
      <view wx:if="{{inputMode === 'text'}}" class="text-input-mode">
        <view class="text-input-wrapper">
          <view class="voice-icon" catchtap="onVoiceIconTap" data-action="toggle">
            <text class="icon">🎤</text>
          </view>
          <input
            class="text-input"
            placeholder="输入消息..."
            value="{{inputValue}}"
            bindinput="onInputChange"
            confirm-type="send"
            bindconfirm="onSendMessage"
            focus="{{shouldFocus}}"
            bindfocus="onInputFocus"
            bindblur="onInputBlur"
          />
          <view wx:if="{{inputValue}}" class="send-btn" bindtap="onSendMessage">
            <text class="send-text">发送</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
