/**
 * 语音处理工具类
 */

const log = console.log;

class VoiceUtils {
  constructor() {
    this.recorderManager = null;
    this.innerAudioContext = null;
    this.isRecording = false;
    this.recordStartTime = 0;
  }

  // 初始化录音管理器
  initRecorder() {
    if (this.recorderManager) {
      return this.recorderManager;
    }

    this.recorderManager = wx.getRecorderManager();
    return this.recorderManager;
  }

  // 初始化音频播放器
  initAudioPlayer() {
    if (this.innerAudioContext) {
      return this.innerAudioContext;
    }

    this.innerAudioContext = wx.createInnerAudioContext();
    return this.innerAudioContext;
  }

  // 检查录音权限
  checkRecordPermission() {
    return new Promise((resolve, reject) => {
      log('=== 开始检查录音权限 ===');

      wx.getSetting({
        success: (res) => {
          log('当前权限设置:', res.authSetting);
          log('录音权限状态:', res.authSetting['scope.record']);

          if (res.authSetting['scope.record'] === false) {
            // 用户之前拒绝了录音权限
            log('用户之前拒绝了录音权限，引导去设置');
            wx.showModal({
              title: '需要录音权限',
              content: '请在设置中开启录音权限以使用语音功能',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      log('设置页面返回:', settingRes.authSetting);
                      if (settingRes.authSetting['scope.record']) {
                        resolve(true);
                      } else {
                        reject(new Error('用户未开启录音权限'));
                      }
                    },
                    fail: () => {
                      reject(new Error('打开设置页面失败'));
                    }
                  });
                } else {
                  reject(new Error('用户取消开启录音权限'));
                }
              }
            });
          } else if (res.authSetting['scope.record'] === undefined) {
            // 未询问过权限，主动申请
            log('首次申请录音权限，使用简化方案');

            // 简化方案：直接尝试启动录音器来触发权限申请
            this.requestPermissionByRecorder()
              .then(() => {
                log('✅ 权限申请成功');
                resolve(true);
              })
              .catch((err) => {
                log('❌ 权限申请失败:', err);
                reject(err);
              });
          } else {
            // 已有权限
            log('已有录音权限');
            resolve(true);
          }
        },
        fail: (err) => {
          log('获取权限设置失败:', err);
          reject(new Error('获取权限设置失败'));
        }
      });
    });
  }

  // 通过录音器申请权限（简化方案）
  requestPermissionByRecorder() {
    return new Promise((resolve, reject) => {
      log('通过录音器申请权限');

      // 使用同一个录音器实例，避免冲突
      const tempRecorder = this.initRecorder();
      let permissionResolved = false;

      // 设置超时，防止无限等待
      const timeout = setTimeout(() => {
        if (!permissionResolved) {
          log('权限申请超时');
          permissionResolved = true;
          reject(new Error('权限申请超时，请在设置中手动开启录音权限'));
        }
      }, 10000); // 增加到10秒

      // 监听录音开始事件
      tempRecorder.onStart(() => {
        log('✅ 录音器启动成功，权限获取成功');
        if (!permissionResolved) {
          permissionResolved = true;
          clearTimeout(timeout);
          // 立即停止临时录音
          setTimeout(() => {
            tempRecorder.stop();
          }, 100);
          resolve(true);
        }
      });

      // 监听录音错误事件
      tempRecorder.onError((err) => {
        log('❌ 录音器启动失败:', err);
        if (!permissionResolved) {
          permissionResolved = true;
          clearTimeout(timeout);

          // 根据错误类型给出不同的提示
          if (err.errMsg && err.errMsg.includes('auth')) {
            reject(new Error('用户拒绝录音权限'));
          } else {
            reject(new Error('录音器启动失败，请检查设备权限'));
          }
        }
      });

      // 尝试启动录音器
      try {
        log('启动临时录音器...');
        tempRecorder.start({
          duration: 1000,
          sampleRate: 16000,
          numberOfChannels: 1,
          encodeBitRate: 96000,
          format: 'mp3'
        });
      } catch (error) {
        log('❌ 启动录音器异常:', error);
        if (!permissionResolved) {
          permissionResolved = true;
          clearTimeout(timeout);
          reject(new Error('无法启动录音器：' + error.message));
        }
      }
    });
  }

  // 简化的录音启动方法（用于调试）
  startRecordSimple(options = {}) {
    return new Promise((resolve, reject) => {
      log('=== 简化录音启动 ===');

      // 重新创建录音器实例以清除旧的事件监听器
      this.recorderManager = null;
      const recorder = this.initRecorder();
      log('重新创建录音器实例');

      const defaultOptions = {
        duration: 60000,
        sampleRate: 16000,
        numberOfChannels: 1,
        encodeBitRate: 96000,
        format: 'mp3'
      };

      const recordOptions = { ...defaultOptions, ...options };
      log('录音配置:', recordOptions);

      // 设置录音开始回调
      recorder.onStart(() => {
        log('✅ 简化录音器启动成功');
        this.isRecording = true;
        this.recordStartTime = Date.now();
        resolve({
          success: true,
          message: '开始录音'
        });
      });

      // 设置录音错误回调
      recorder.onError((err) => {
        log('❌ 简化录音器错误:', err);
        this.isRecording = false;
        reject(new Error('录音失败：' + (err.errMsg || err.message || '未知错误')));
      });

      // 直接启动录音器，不检查权限
      try {
        log('直接启动录音器...');
        recorder.start(recordOptions);
      } catch (error) {
        log('❌ 启动录音器异常:', error);
        this.isRecording = false;
        reject(new Error('启动录音失败：' + error.message));
      }
    });
  }

  // 开始录音
  startRecord(options = {}) {
    return new Promise((resolve, reject) => {
      log('startRecord 被调用');

      this.checkRecordPermission()
        .then(() => {
          log('权限检查通过，开始初始化录音器');

          // 微信小程序录音管理器没有off方法，重新创建实例来清除事件监听器
          this.recorderManager = null;
          const recorder = this.initRecorder();
          log('重新创建录音器实例以清除旧的事件监听器');

          const defaultOptions = {
            duration: 60000, // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: 'mp3'
          };

          const recordOptions = { ...defaultOptions, ...options };
          log('录音配置:', recordOptions);

          // 设置录音开始回调
          recorder.onStart(() => {
            log('✅ 正式录音器启动成功');
            this.isRecording = true;
            this.recordStartTime = Date.now();
            resolve({
              success: true,
              message: '开始录音'
            });
          });

          // 设置录音错误回调
          recorder.onError((err) => {
            log('❌ 正式录音器错误:', err);
            this.isRecording = false;
            reject(new Error('录音失败，请检查麦克风权限'));
          });

          // 开始录音
          try {
            log('调用 recorder.start()');
            recorder.start(recordOptions);
          } catch (error) {
            log('启动录音器异常:', error);
            this.isRecording = false;
            reject(new Error('启动录音失败，请检查麦克风权限'));
          }
        })
        .catch((error) => {
          log('权限检查失败:', error);
          reject(error);
        });
    });
  }

  // 停止录音
  stopRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        reject(new Error('当前没有在录音'));
        return;
      }

      const recorder = this.recorderManager;
      
      recorder.onStop((res) => {
        this.isRecording = false;
        const duration = Math.floor((Date.now() - this.recordStartTime) / 1000);
        
        resolve({
          tempFilePath: res.tempFilePath,
          duration: duration,
          fileSize: res.fileSize
        });
      });

      recorder.stop();
    });
  }

  // 播放音频
  playAudio(filePath) {
    return new Promise((resolve, reject) => {
      const audioContext = this.initAudioPlayer();
      
      audioContext.src = filePath;
      
      audioContext.onPlay(() => {
        resolve({
          success: true,
          message: '开始播放'
        });
      });

      audioContext.onEnded(() => {
        resolve({
          success: true,
          message: '播放结束'
        });
      });

      audioContext.onError((err) => {
        reject(err);
      });

      audioContext.play();
    });
  }

  // 停止播放
  stopAudio() {
    if (this.innerAudioContext) {
      this.innerAudioContext.stop();
    }
  }

  // 暂停播放
  pauseAudio() {
    if (this.innerAudioContext) {
      this.innerAudioContext.pause();
    }
  }

  // 获取录音时长
  getRecordDuration() {
    if (!this.isRecording) {
      return 0;
    }
    return Math.floor((Date.now() - this.recordStartTime) / 1000);
  }

  // 格式化时长显示
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // 语音转文字（使用微信小程序语音识别API）
  speechToText(filePath) {
    return new Promise((resolve, reject) => {
      // 检查是否支持语音识别
      if (!wx.startRecord || !wx.stopRecord) {
        // 显示降级提示
        wx.showToast({
          title: '使用模拟识别功能',
          icon: 'none',
          duration: 1500
        });
        this.fallbackSpeechToText().then(resolve).catch(reject);
        return;
      }

      // 使用微信的语音识别接口
      wx.translateVoice({
        filePath: filePath,
        isShowProgressTips: 1, // 显示进度提示
        success: (res) => {
          // 识别成功，不需要额外提示，由调用方处理
          resolve({
            success: true,
            text: res.result || '',
            confidence: 0.9,
            duration: res.duration || 0
          });
        },
        fail: (err) => {
          // 根据错误信息处理
          let errorMessage = '语音识别失败';
          let shouldShowToast = true;

          if (err.errMsg) {
            if (err.errMsg.includes('network')) {
              errorMessage = '网络连接失败，使用模拟识别';
              shouldShowToast = false; // 降级时不显示错误提示
            } else if (err.errMsg.includes('timeout')) {
              errorMessage = '识别超时，请重试';
            } else if (err.errMsg.includes('format')) {
              errorMessage = '音频格式不支持';
            } else if (err.errMsg.includes('duration')) {
              errorMessage = '音频时长不符合要求';
            }
          }

          // 网络问题时降级到模拟实现
          if (err.errMsg && err.errMsg.includes('network')) {
            wx.showToast({
              title: '网络不佳，使用模拟识别',
              icon: 'none',
              duration: 1500
            });
            this.fallbackSpeechToText().then(resolve).catch(reject);
          } else {
            // 显示具体错误信息
            if (shouldShowToast) {
              wx.showToast({
                title: errorMessage,
                icon: 'none',
                duration: 2000
              });
            }
            reject(new Error(errorMessage));
          }
        }
      });
    });
  }

  // 降级的模拟语音识别实现
  fallbackSpeechToText() {
    return new Promise((resolve) => {
      log('使用模拟语音识别');
      setTimeout(() => {
        const mockTexts = [
          '你好，我想咨询一下产品信息',
          '请问这个商品有什么优惠活动吗',
          '我想了解一下售后服务',
          '这个产品的质量怎么样',
          '能帮我推荐一些类似的产品吗'
        ];

        const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];

        resolve({
          success: true,
          text: `[模拟识别] ${randomText}`,
          confidence: 0.85,
          isFallback: true
        });
      }, 1000);
    });
  }

  // 测试录音器状态
  testRecorderStatus() {
    log('=== 录音器状态检查 ===');
    log('recorderManager 存在:', !!this.recorderManager);
    log('isRecording 状态:', this.isRecording);
    log('recordStartTime:', this.recordStartTime);

    if (this.recorderManager) {
      log('录音器实例:', this.recorderManager);
    }

    // 测试权限状态
    wx.getSetting({
      success: (res) => {
        log('当前权限状态:', res.authSetting['scope.record']);
      }
    });
  }

  // 销毁资源
  destroy() {
    if (this.recorderManager && this.isRecording) {
      this.recorderManager.stop();
    }

    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }

    this.recorderManager = null;
    this.isRecording = false;
  }
}

// 创建单例
const voiceUtils = new VoiceUtils();

module.exports = voiceUtils;
