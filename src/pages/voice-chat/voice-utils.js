const log = console.log;

class VoiceUtils {
  constructor() {
    this.recorderManager = null;
    this.innerAudioContext = null;
    this.isRecording = false;
    this.recordStartTime = null;
  }

  // 初始化录音器
  initRecorder() {
    if (this.recorderManager) {
      return this.recorderManager;
    }
    this.recorderManager = wx.getRecorderManager();
    return this.recorderManager;
  }

  // 初始化音频播放器
  initAudioContext() {
    if (this.innerAudioContext) {
      return this.innerAudioContext;
    }
    this.innerAudioContext = wx.createInnerAudioContext();
    return this.innerAudioContext;
  }

  // 检查录音权限
  checkRecordPermission() {
    return new Promise((resolve, reject) => {
      log('开始检查录音权限');

      wx.getSetting({
        success: (res) => {
          log('权限设置:', res.authSetting);

          if (res.authSetting['scope.record'] === true) {
            log('已有录音权限');
            resolve(true);
          } else if (res.authSetting['scope.record'] === false) {
            log('录音权限被拒绝，引导用户到设置页面');
            wx.showModal({
              title: '需要录音权限',
              content: '请在设置中开启录音权限以使用语音功能',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        resolve(true);
                      } else {
                        reject(new Error('用户未开启录音权限'));
                      }
                    }
                  });
                } else {
                  reject(new Error('用户取消开启权限'));
                }
              }
            });
          } else if (res.authSetting['scope.record'] === undefined) {
            // 未询问过权限，主动申请
            log('首次申请录音权限');
            wx.authorize({
              scope: 'scope.record',
              success: () => {
                log('录音权限申请成功');
                resolve(true);
              },
              fail: (err) => {
                log('录音权限申请失败:', err);
                reject(new Error('用户拒绝录音权限'));
              }
            });
          }
        },
        fail: (err) => {
          log('获取权限设置失败:', err);
          reject(new Error('获取权限设置失败'));
        }
      });
    });
  }

  // 开始录音
  startRecord(options = {}) {
    return new Promise((resolve, reject) => {
      log('startRecord 被调用');

      this.checkRecordPermission()
        .then(() => {
          log('权限检查通过，开始初始化录音器');

          const recorder = this.initRecorder();

          const defaultOptions = {
            duration: 60000, // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: 'mp3'
          };

          const recordOptions = { ...defaultOptions, ...options };
          log('录音配置:', recordOptions);

          // 设置录音开始回调
          recorder.onStart(() => {
            log('录音器启动成功');
            this.isRecording = true;
            this.recordStartTime = Date.now();
            resolve({
              success: true,
              message: '开始录音'
            });
          });

          // 设置录音错误回调
          recorder.onError((err) => {
            log('录音器错误:', err);
            this.isRecording = false;
            reject(new Error('录音失败，请检查麦克风权限'));
          });

          // 开始录音
          try {
            log('调用 recorder.start()');
            recorder.start(recordOptions);
          } catch (error) {
            log('启动录音器异常:', error);
            this.isRecording = false;
            reject(new Error('启动录音失败，请检查麦克风权限'));
          }
        })
        .catch((error) => {
          log('权限检查失败:', error);
          reject(error);
        });
    });
  }

  // 停止录音
  stopRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        reject(new Error('当前没有在录音'));
        return;
      }

      const recorder = this.recorderManager;
      if (!recorder) {
        reject(new Error('录音器未初始化'));
        return;
      }

      // 设置录音停止回调
      recorder.onStop((res) => {
        log('录音停止:', res);
        this.isRecording = false;

        const duration = Date.now() - this.recordStartTime;
        log('录音时长:', duration, 'ms');

        resolve({
          tempFilePath: res.tempFilePath,
          duration: res.duration || duration,
          fileSize: res.fileSize
        });
      });

      // 停止录音
      try {
        recorder.stop();
      } catch (error) {
        log('停止录音异常:', error);
        this.isRecording = false;
        reject(new Error('停止录音失败'));
      }
    });
  }

  // 获取当前录音时长（秒）
  getRecordDuration() {
    if (!this.isRecording || !this.recordStartTime) {
      return 0;
    }

    const duration = Math.floor((Date.now() - this.recordStartTime) / 1000);
    return duration;
  }

  // 播放音频
  playAudio(filePath) {
    return new Promise((resolve, reject) => {
      const audioContext = this.initAudioContext();

      audioContext.src = filePath;

      audioContext.onPlay(() => {
        log('音频开始播放');
      });

      audioContext.onEnded(() => {
        log('音频播放结束');
        resolve();
      });

      audioContext.onError((err) => {
        log('音频播放错误:', err);
        reject(new Error('音频播放失败'));
      });

      audioContext.play();
    });
  }

  // 语音转文字（使用微信小程序原生API）
  speechToText(filePath) {
    return new Promise((resolve, reject) => {
      log('开始语音转文字:', filePath);

      // 检查文件路径
      if (!filePath) {
        log('语音文件路径为空');
        reject(new Error('语音文件路径无效'));
        return;
      }

      // 检查微信语音识别API是否可用
      if (typeof wx.createRecognitionManager !== 'function') {
        log('当前环境不支持语音识别API，使用模拟数据');
        this.mockSpeechToText().then(resolve).catch(reject);
        return;
      }

      try {
        // 创建语音识别管理器
        const recognitionManager = wx.createRecognitionManager();

        // 设置识别开始回调
        recognitionManager.onStart(() => {
          log('语音识别开始');
        });

        // 设置识别结果回调
        recognitionManager.onRecognize((res) => {
          log('语音识别中间结果:', res.result);
        });

        // 设置识别结束回调
        recognitionManager.onStop((res) => {
          log('语音识别结束:', res);
          if (res.result && res.result.length > 0) {
            resolve({
              text: res.result,
              confidence: 0.9
            });
          } else {
            reject(new Error('语音识别无结果'));
          }
        });

        // 设置识别错误回调
        recognitionManager.onError((err) => {
          log('语音识别错误:', err);

          let errorMessage = '语音识别失败';
          switch (err.errCode) {
            case 10003:
              errorMessage = '网络连接失败，请检查网络';
              break;
            case 10007:
              errorMessage = '语音过短，请重新录制';
              break;
            case 10008:
              errorMessage = '语音过长，请控制在60秒内';
              break;
            case 10009:
              errorMessage = '语音识别服务暂时不可用';
              break;
            default:
              errorMessage = `语音识别失败 (${err.errCode})`;
          }

          reject(new Error(errorMessage));
        });

        // 开始识别
        recognitionManager.start({
          lang: 'zh_CN' // 中文识别
        });

      } catch (error) {
        log('创建语音识别管理器失败:', error);
        // 降级到模拟实现
        this.mockSpeechToText().then(resolve).catch(reject);
      }
    });
  }



  // 模拟语音转文字（降级方案）
  mockSpeechToText() {
    return new Promise((resolve) => {
      log('使用模拟语音转文字');

      // 模拟文本库
      const mockTexts = [
        '你好，我想了解一下产品信息',
        '请问有什么优惠活动吗？',
        '我要预订明天的服务',
        '谢谢你的帮助',
        '再见',
        '我想咨询一下价格',
        '这个产品怎么样？',
        '有没有其他颜色的？',
        '什么时候能发货？',
        '支持退换货吗？'
      ];

      // 模拟网络请求延迟
      setTimeout(() => {
        // 随机选择一个模拟文本
        const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];

        log('模拟语音转文字结果:', randomText);
        resolve({
          text: randomText,
          confidence: 0.85,
          isMock: true
        });
      }, 1500);
    });
  }

  // 销毁资源
  destroy() {
    if (this.recorderManager && this.isRecording) {
      this.recorderManager.stop();
    }
    
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }
    
    this.recorderManager = null;
    this.isRecording = false;
  }
}

// 创建单例
const voiceUtils = new VoiceUtils();

module.exports = voiceUtils;
