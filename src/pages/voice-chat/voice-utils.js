const log = console.log;
const speechConfig = require('./speech-config.js');

class VoiceUtils {
  constructor() {
    this.recorderManager = null;
    this.innerAudioContext = null;
    this.isRecording = false;
    this.recordStartTime = null;
  }

  // 初始化录音器
  initRecorder() {
    if (this.recorderManager) {
      return this.recorderManager;
    }
    this.recorderManager = wx.getRecorderManager();
    return this.recorderManager;
  }

  // 初始化音频播放器
  initAudioContext() {
    if (this.innerAudioContext) {
      return this.innerAudioContext;
    }
    this.innerAudioContext = wx.createInnerAudioContext();
    return this.innerAudioContext;
  }

  // 检查录音权限
  checkRecordPermission() {
    return new Promise((resolve, reject) => {
      log('开始检查录音权限');

      wx.getSetting({
        success: (res) => {
          log('权限设置:', res.authSetting);

          if (res.authSetting['scope.record'] === true) {
            log('已有录音权限');
            resolve(true);
          } else if (res.authSetting['scope.record'] === false) {
            log('录音权限被拒绝，引导用户到设置页面');
            wx.showModal({
              title: '需要录音权限',
              content: '请在设置中开启录音权限以使用语音功能',
              confirmText: '去设置',
              cancelText: '取消',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        resolve(true);
                      } else {
                        reject(new Error('用户未开启录音权限'));
                      }
                    }
                  });
                } else {
                  reject(new Error('用户取消开启权限'));
                }
              }
            });
          } else if (res.authSetting['scope.record'] === undefined) {
            // 未询问过权限，主动申请
            log('首次申请录音权限');
            wx.authorize({
              scope: 'scope.record',
              success: () => {
                log('录音权限申请成功');
                resolve(true);
              },
              fail: (err) => {
                log('录音权限申请失败:', err);
                reject(new Error('用户拒绝录音权限'));
              }
            });
          }
        },
        fail: (err) => {
          log('获取权限设置失败:', err);
          reject(new Error('获取权限设置失败'));
        }
      });
    });
  }

  // 开始录音
  startRecord(options = {}) {
    return new Promise((resolve, reject) => {
      log('startRecord 被调用');

      this.checkRecordPermission()
        .then(() => {
          log('权限检查通过，开始初始化录音器');

          const recorder = this.initRecorder();

          const defaultOptions = {
            duration: 60000, // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: 'mp3'
          };

          const recordOptions = { ...defaultOptions, ...options };
          log('录音配置:', recordOptions);

          // 设置录音开始回调
          recorder.onStart(() => {
            log('录音器启动成功');
            this.isRecording = true;
            this.recordStartTime = Date.now();
            resolve({
              success: true,
              message: '开始录音'
            });
          });

          // 设置录音错误回调
          recorder.onError((err) => {
            log('录音器错误:', err);
            this.isRecording = false;
            reject(new Error('录音失败，请检查麦克风权限'));
          });

          // 开始录音
          try {
            log('调用 recorder.start()');
            recorder.start(recordOptions);
          } catch (error) {
            log('启动录音器异常:', error);
            this.isRecording = false;
            reject(new Error('启动录音失败，请检查麦克风权限'));
          }
        })
        .catch((error) => {
          log('权限检查失败:', error);
          reject(error);
        });
    });
  }

  // 停止录音
  stopRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        reject(new Error('当前没有在录音'));
        return;
      }

      const recorder = this.recorderManager;
      if (!recorder) {
        reject(new Error('录音器未初始化'));
        return;
      }

      // 设置录音停止回调
      recorder.onStop((res) => {
        log('录音停止:', res);
        this.isRecording = false;
        
        const duration = Date.now() - this.recordStartTime;
        log('录音时长:', duration, 'ms');

        resolve({
          tempFilePath: res.tempFilePath,
          duration: res.duration || duration,
          fileSize: res.fileSize
        });
      });

      // 停止录音
      try {
        recorder.stop();
      } catch (error) {
        log('停止录音异常:', error);
        this.isRecording = false;
        reject(new Error('停止录音失败'));
      }
    });
  }

  // 播放音频
  playAudio(filePath) {
    return new Promise((resolve, reject) => {
      const audioContext = this.initAudioContext();

      audioContext.src = filePath;

      audioContext.onPlay(() => {
        log('音频开始播放');
      });

      audioContext.onEnded(() => {
        log('音频播放结束');
        resolve();
      });

      audioContext.onError((err) => {
        log('音频播放错误:', err);
        reject(new Error('音频播放失败'));
      });

      audioContext.play();
    });
  }

  // 语音转文字（根据配置选择不同的识别服务）
  speechToText(filePath) {
    return new Promise((resolve, reject) => {
      log('开始语音转文字:', filePath);
      log('使用语音识别服务:', speechConfig.provider);

      // 检查文件路径
      if (!filePath) {
        log('语音文件路径为空，使用模拟数据');
        this.mockSpeechToText().then(resolve).catch(reject);
        return;
      }

      // 根据配置选择不同的识别服务
      switch (speechConfig.provider) {
        case 'baidu':
          this.useBaiduSpeechAPI(filePath)
            .then(resolve)
            .catch((err) => {
              log('百度语音识别失败:', err);
              log('降级到模拟实现');
              this.mockSpeechToText().then(resolve).catch(reject);
            });
          break;

        case 'wxcloud':
          this.useWxCloudSpeechAPI(filePath)
            .then(resolve)
            .catch((err) => {
              log('微信云开发语音识别失败:', err);
              log('降级到模拟实现');
              this.mockSpeechToText().then(resolve).catch(reject);
            });
          break;

        case 'mock':
        default:
          this.mockSpeechToText().then(resolve).catch(reject);
          break;
      }
    });
  }

  // 使用百度语音识别API（需要服务器支持）
  useBaiduSpeechAPI(filePath) {
    return new Promise((resolve, reject) => {
      log('使用百度语音识别API');

      const config = speechConfig.baidu;

      // 上传文件到服务器进行识别
      wx.uploadFile({
        url: config.apiUrl,
        filePath: filePath,
        name: 'audio',
        timeout: config.timeout,
        header: {
          'Content-Type': 'multipart/form-data'
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success && data.text) {
              log('百度语音识别成功:', data.text);
              resolve({
                text: data.text,
                confidence: data.confidence || 0.9,
                provider: 'baidu'
              });
            } else {
              reject(new Error(data.message || '语音识别失败'));
            }
          } catch (e) {
            log('解析百度API响应失败:', e);
            reject(new Error('解析识别结果失败'));
          }
        },
        fail: (err) => {
          log('调用百度API失败:', err);
          reject(new Error('网络连接失败'));
        }
      });
    });
  }

  // 使用微信云开发语音识别（如果有云开发环境）
  useWxCloudSpeechAPI(filePath) {
    return new Promise((resolve, reject) => {
      log('使用微信云开发语音识别');

      // 检查云开发是否可用
      if (typeof wx.cloud === 'undefined') {
        reject(new Error('云开发环境不可用'));
        return;
      }

      const config = speechConfig.wxcloud;

      // 调用云函数进行语音识别
      wx.cloud.callFunction({
        name: config.functionName,
        data: {
          filePath: filePath
        },
        timeout: config.timeout,
        success: (res) => {
          if (res.result && res.result.success) {
            log('微信云开发语音识别成功:', res.result.text);
            resolve({
              text: res.result.text,
              confidence: res.result.confidence || 0.9,
              provider: 'wxcloud'
            });
          } else {
            reject(new Error(res.result.message || '语音识别失败'));
          }
        },
        fail: (err) => {
          log('调用云函数失败:', err);
          reject(new Error('语音识别服务不可用'));
        }
      });
    });
  }

  // 模拟语音转文字（降级方案）
  mockSpeechToText() {
    return new Promise((resolve) => {
      log('使用模拟语音转文字');

      // 使用配置中的延迟时间和文本库
      const delay = speechConfig.mock.delay;
      const texts = speechConfig.mock.texts;

      // 模拟网络请求延迟
      setTimeout(() => {
        // 随机选择一个模拟文本
        const randomText = texts[Math.floor(Math.random() * texts.length)];

        log('模拟语音转文字结果:', randomText);
        resolve({
          text: randomText,
          confidence: 0.85,
          isMock: true
        });
      }, delay);
    });
  }

  // 销毁资源
  destroy() {
    if (this.recorderManager && this.isRecording) {
      this.recorderManager.stop();
    }
    
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }
    
    this.recorderManager = null;
    this.isRecording = false;
  }
}

// 创建单例
const voiceUtils = new VoiceUtils();

module.exports = voiceUtils;
