/**
 * 语音处理工具类
 */

class VoiceUtils {
  constructor() {
    this.recorderManager = null;
    this.innerAudioContext = null;
    this.isRecording = false;
    this.recordStartTime = 0;
  }

  // 初始化录音管理器
  initRecorder() {
    if (this.recorderManager) {
      return this.recorderManager;
    }

    this.recorderManager = wx.getRecorderManager();
    return this.recorderManager;
  }

  // 初始化音频播放器
  initAudioPlayer() {
    if (this.innerAudioContext) {
      return this.innerAudioContext;
    }

    this.innerAudioContext = wx.createInnerAudioContext();
    return this.innerAudioContext;
  }

  // 检查录音权限
  checkRecordPermission() {
    return new Promise((resolve, reject) => {
      wx.getSetting({
        success: (res) => {
          if (res.authSetting['scope.record'] === false) {
            // 用户拒绝了录音权限
            wx.showModal({
              title: '需要录音权限',
              content: '请在设置中开启录音权限以使用语音功能',
              confirmText: '去设置',
              success: (modalRes) => {
                if (modalRes.confirm) {
                  wx.openSetting({
                    success: (settingRes) => {
                      if (settingRes.authSetting['scope.record']) {
                        resolve(true);
                      } else {
                        reject(new Error('用户未开启录音权限'));
                      }
                    }
                  });
                } else {
                  reject(new Error('用户取消开启录音权限'));
                }
              }
            });
          } else if (res.authSetting['scope.record'] === undefined) {
            // 未询问过权限，主动申请
            wx.authorize({
              scope: 'scope.record',
              success: () => resolve(true),
              fail: () => reject(new Error('用户拒绝录音权限'))
            });
          } else {
            // 已有权限
            resolve(true);
          }
        },
        fail: () => reject(new Error('获取权限设置失败'))
      });
    });
  }

  // 开始录音
  startRecord(options = {}) {
    return new Promise((resolve, reject) => {
      this.checkRecordPermission()
        .then(() => {
          const recorder = this.initRecorder();
          
          const defaultOptions = {
            duration: 60000, // 最长60秒
            sampleRate: 16000,
            numberOfChannels: 1,
            encodeBitRate: 96000,
            format: 'mp3'
          };

          const recordOptions = { ...defaultOptions, ...options };

          recorder.onStart(() => {
            this.isRecording = true;
            this.recordStartTime = Date.now();
            resolve({
              success: true,
              message: '开始录音'
            });
          });

          recorder.onError((err) => {
            this.isRecording = false;
            console.error('录音错误:', err);
            reject(new Error('录音失败，请检查麦克风权限'));
          });

          // 开始录音
          try {
            recorder.start(recordOptions);
          } catch (error) {
            this.isRecording = false;
            console.error('启动录音失败:', error);
            reject(new Error('启动录音失败，请检查麦克风权限'));
          }
        })
        .catch((error) => {
          console.error('权限检查失败:', error);
          reject(error);
        });
    });
  }

  // 停止录音
  stopRecord() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        reject(new Error('当前没有在录音'));
        return;
      }

      const recorder = this.recorderManager;
      
      recorder.onStop((res) => {
        this.isRecording = false;
        const duration = Math.floor((Date.now() - this.recordStartTime) / 1000);
        
        resolve({
          tempFilePath: res.tempFilePath,
          duration: duration,
          fileSize: res.fileSize
        });
      });

      recorder.stop();
    });
  }

  // 播放音频
  playAudio(filePath) {
    return new Promise((resolve, reject) => {
      const audioContext = this.initAudioPlayer();
      
      audioContext.src = filePath;
      
      audioContext.onPlay(() => {
        resolve({
          success: true,
          message: '开始播放'
        });
      });

      audioContext.onEnded(() => {
        resolve({
          success: true,
          message: '播放结束'
        });
      });

      audioContext.onError((err) => {
        reject(err);
      });

      audioContext.play();
    });
  }

  // 停止播放
  stopAudio() {
    if (this.innerAudioContext) {
      this.innerAudioContext.stop();
    }
  }

  // 暂停播放
  pauseAudio() {
    if (this.innerAudioContext) {
      this.innerAudioContext.pause();
    }
  }

  // 获取录音时长
  getRecordDuration() {
    if (!this.isRecording) {
      return 0;
    }
    return Math.floor((Date.now() - this.recordStartTime) / 1000);
  }

  // 格式化时长显示
  formatDuration(seconds) {
    if (seconds < 60) {
      return `${seconds}s`;
    }
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  // 语音转文字（使用微信小程序语音识别API）
  speechToText(filePath) {
    return new Promise((resolve, reject) => {
      // 检查是否支持语音识别
      if (!wx.startRecord || !wx.stopRecord) {
        console.warn('当前微信版本不支持语音识别，使用模拟实现');
        this.fallbackSpeechToText().then(resolve).catch(reject);
        return;
      }

      // 使用微信的语音识别接口
      wx.translateVoice({
        filePath: filePath,
        isShowProgressTips: 1, // 显示进度提示
        success: (res) => {
          console.log('语音识别成功:', res);
          resolve({
            success: true,
            text: res.result || '',
            confidence: 0.9,
            duration: res.duration || 0
          });
        },
        fail: (err) => {
          console.error('语音识别失败:', err);

          // 根据错误信息处理
          let errorMessage = '语音识别失败';
          if (err.errMsg) {
            if (err.errMsg.includes('network')) {
              errorMessage = '网络连接失败，请检查网络';
            } else if (err.errMsg.includes('timeout')) {
              errorMessage = '识别超时，请重试';
            } else if (err.errMsg.includes('format')) {
              errorMessage = '音频格式不支持';
            } else if (err.errMsg.includes('duration')) {
              errorMessage = '音频时长不符合要求';
            }
          }

          // 网络问题时降级到模拟实现
          if (err.errMsg && err.errMsg.includes('network')) {
            console.log('网络问题，降级到模拟语音识别');
            this.fallbackSpeechToText().then(resolve).catch(reject);
          } else {
            reject(new Error(errorMessage));
          }
        }
      });
    });
  }

  // 降级的模拟语音识别实现
  fallbackSpeechToText() {
    return new Promise((resolve) => {
      console.log('使用模拟语音识别');
      setTimeout(() => {
        const mockTexts = [
          '你好，我想咨询一下产品信息',
          '请问这个商品有什么优惠活动吗',
          '我想了解一下售后服务',
          '这个产品的质量怎么样',
          '能帮我推荐一些类似的产品吗'
        ];

        const randomText = mockTexts[Math.floor(Math.random() * mockTexts.length)];

        resolve({
          success: true,
          text: `[模拟识别] ${randomText}`,
          confidence: 0.85,
          isFallback: true
        });
      }, 1000);
    });
  }

  // 销毁资源
  destroy() {
    if (this.recorderManager && this.isRecording) {
      this.recorderManager.stop();
    }
    
    if (this.innerAudioContext) {
      this.innerAudioContext.destroy();
      this.innerAudioContext = null;
    }
    
    this.recorderManager = null;
    this.isRecording = false;
  }
}

// 创建单例
const voiceUtils = new VoiceUtils();

module.exports = voiceUtils;
