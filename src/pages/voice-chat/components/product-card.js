// 商品卡片组件
Component({
  properties: {
    // 商品信息
    product: {
      type: Object,
      value: {}
    },
    // 卡片类型：purchase(购买) 或 order(订单)
    type: {
      type: String,
      value: 'purchase'
    },
    // 是否显示
    visible: {
      type: Boolean,
      value: true
    }
  },

  methods: {
    // 修改商品规格
    onModifySpecs() {
      console.log('修改商品规格');
      this.triggerEvent('modifySpecs', {
        product: this.data.product
      });
    },

    // 查看价格并支付
    onViewPriceAndPay() {
      console.log('查看价格并支付');
      this.triggerEvent('viewPriceAndPay', {
        product: this.data.product
      });
    },

    // 查看订单详情
    onViewOrderDetail() {
      console.log('查看订单详情');
      this.triggerEvent('viewOrderDetail', {
        product: this.data.product
      });
    }
  }
});
