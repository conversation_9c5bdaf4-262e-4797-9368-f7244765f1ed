/* 收银台样式 */
.cashier-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999; /* 最高层级 */
  display: flex;
  align-items: flex-end;
}

.cashier-container {
  width: 100%;
  background: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.cashier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.close-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #666;
}

.qr-code-btn {
  color: #1976d2;
  font-size: 16px;
}

.store-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 20px;
}

.store-name {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.play-icon {
  width: 24px;
  height: 24px;
  background: #666;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.amount-section {
  text-align: center;
  margin-bottom: 30px;
}

.currency {
  font-size: 24px;
  color: #333;
}

.amount {
  font-size: 48px;
  font-weight: bold;
  color: #333;
}

.order-section {
  margin-bottom: 20px;
}

.section-title {
  font-size: 16px;
  color: #333;
  margin-bottom: 10px;
}

.order-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.detail-right {
  color: #333;
  font-size: 14px;
}

.payment-section {
  margin-bottom: 20px;
}

.payment-method {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #f0f0f0;
}

.method-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.method-icon {
  width: 24px;
  height: 24px;
}

.method-name {
  font-size: 16px;
  color: #333;
}

.method-status {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: #1976d2;
  font-size: 16px;
  font-weight: bold;
}

.expand-section {
  text-align: center;
  padding: 10px 0;
  margin-bottom: 20px;
}

.expand-icon {
  color: #666;
  font-size: 16px;
}

.confirm-section {
  text-align: center;
}

.confirm-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 25px;
  padding: 15px;
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 10px;
}

.service-provider {
  font-size: 12px;
  color: #999;
}
