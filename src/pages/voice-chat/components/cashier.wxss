/* 收银台样式 */
.cashier-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999; /* 最高层级 */
  display: flex;
  align-items: flex-end;
}

.cashier-container {
  width: 100%;
  background: white;
  border-radius: 20px 20px 0 0;
  padding: 20px;
  max-height: 180vh;
  overflow-y: auto;
}

.cashier-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.header-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.close-btn {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #666;
}

.store-section {
  text-align: center;
  margin-bottom: 30px;
}

.store-name {
  font-size: 16px;
  color: #666;
}

.amount-section {
  text-align: center;
  margin-bottom: 40px;
}

.amount {
  font-size: 48px;
  font-weight: bold;
  color: #333;
}

.payment-section {
  margin-bottom: 40px;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-title {
  font-size: 16px;
  color: #333;
}

.payment-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.method-icon {
  width: 20px;
  height: 20px;
  background: #ffd700;
  color: #333;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.method-name {
  font-size: 16px;
  color: #333;
}

.arrow-icon {
  font-size: 16px;
  color: #999;
}

.payment-hint-row {
  display: flex;
  justify-content: flex-end;
}

.method-hint {
  font-size: 12px;
  color: #999;
}

.confirm-section {
  margin-top: 30px;
}

.confirm-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px;
  font-size: 16px;
  font-weight: 500;
}

.confirm-btn::after {
  border: none;
}
