<!-- 收银台组件 -->
<view class="cashier-overlay" wx:if="{{visible}}" bindtap="onOverlayTap">
  <view class="cashier-container" catchtap="onContainerTap">
    <!-- 头部 -->
    <view class="cashier-header">
      <view class="close-btn" bindtap="onClose">×</view>
      <view class="qr-code-btn" bindtap="onUseQRCode">使用密码</view>
    </view>

    <!-- 店铺信息 -->
    <view class="store-section">
      <text class="store-name">{{orderInfo.storeName}}</text>
      <view class="play-icon" wx:if="{{orderInfo.hasAudio}}">
        <text>▶</text>
      </view>
    </view>

    <!-- 金额显示 -->
    <view class="amount-section">
      <text class="currency">¥</text>
      <text class="amount">{{orderInfo.amount}}</text>
    </view>

    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="order-detail">
        <text class="detail-right">{{orderInfo.storeName}}</text>
      </view>
    </view>

    <!-- 支付方式 -->
    <view class="payment-section">
      <view class="payment-method" 
            wx:for="{{paymentMethods}}" 
            wx:key="id"
            bindtap="onSelectPayment"
            data-method="{{item}}">
        <view class="method-info">
          <image class="method-icon" src="{{item.icon}}" mode="aspectFit"></image>
          <text class="method-name">{{item.name}}</text>
        </view>
        <view class="method-status">
          <text wx:if="{{item.selected}}" class="selected-icon">✓</text>
        </view>
      </view>
    </view>

    <!-- 展开更多支付方式 -->
    <view class="expand-section" bindtap="onExpandPayments">
      <text class="expand-icon">∨</text>
    </view>

    <!-- 确认支付按钮 -->
    <view class="confirm-section">
      <button class="confirm-btn" bindtap="onConfirmPayment">确认付款</button>
      <text class="service-provider">本服务由支付宝（杭州）信息技术有限公司提供</text>
    </view>
  </view>
</view>
