<!-- 商品卡片组件 -->
<view class="product-card" wx:if="{{visible}}">
  <view class="card-header">
    <view class="store-info">
      <text class="store-name">{{product.storeName}}</text>
      <view class="play-icon" wx:if="{{product.hasAudio}}">
        <text>▶</text>
      </view>
    </view>
  </view>

  <view class="product-info">
    <image class="product-image" src="{{product.image}}" mode="aspectFill"></image>
    <view class="product-details">
      <text class="product-name">{{product.name}}</text>
      <text class="product-calories">{{product.calories}}</text>
      <text class="product-specs">{{product.specs}}</text>
      <text class="product-quantity">共 {{product.quantity}} 杯</text>
    </view>
  </view>

  <view class="card-actions">
    <view wx:if="{{type === 'purchase'}}" class="purchase-actions">
      <button class="modify-btn" bindtap="onModifySpecs">修改商品规格</button>
      <button class="pay-btn" bindtap="onViewPriceAndPay">查看价格并支付</button>
    </view>
    
    <view wx:if="{{type === 'order'}}" class="order-actions">
      <button class="order-detail-btn" bindtap="onViewOrderDetail">查看订单详情</button>
    </view>
  </view>
</view>
