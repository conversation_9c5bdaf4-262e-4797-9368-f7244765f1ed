// 收银台组件
Component({
  properties: {
    // 是否显示
    visible: {
      type: Boolean,
      value: false
    },
    // 订单信息
    orderInfo: {
      type: Object,
      value: {}
    }
  },

  data: {
    // 支付方式列表
    paymentMethods: [
      {
        id: 'huabei',
        name: '花呗',
        icon: '/images/huabei-icon.png',
        selected: true
      },
      {
        id: 'icbc',
        name: '工商银行储蓄卡 (7980)',
        icon: '/images/icbc-icon.png',
        selected: false
      },
      {
        id: 'yuebao',
        name: '余额宝',
        icon: '/images/yuebao-icon.png',
        selected: false
      }
    ]
  },

  methods: {
    // 关闭收银台
    onClose() {
      this.triggerEvent('close');
    },

    // 点击遮罩层关闭
    onOverlayTap() {
      this.onClose();
    },

    // 阻止容器点击事件冒泡
    onContainerTap() {
      // 阻止冒泡
    },

    // 使用密码
    onUseQRCode() {
      console.log('使用密码');
      // 可以触发密码输入弹窗
    },

    // 选择支付方式
    onSelectPayment(e) {
      const selectedMethod = e.currentTarget.dataset.method;
      const paymentMethods = this.data.paymentMethods.map(method => ({
        ...method,
        selected: method.id === selectedMethod.id
      }));
      
      this.setData({
        paymentMethods
      });
    },

    // 展开更多支付方式
    onExpandPayments() {
      console.log('展开更多支付方式');
      // 可以显示更多支付方式
    },

    // 确认支付
    onConfirmPayment() {
      console.log('确认支付');
      
      // 获取选中的支付方式
      const selectedMethod = this.data.paymentMethods.find(method => method.selected);
      
      // 模拟支付过程
      wx.showLoading({
        title: '支付中...'
      });

      setTimeout(() => {
        wx.hideLoading();
        
        // 触发支付成功事件
        this.triggerEvent('paymentSuccess', {
          orderInfo: this.data.orderInfo,
          paymentMethod: selectedMethod
        });
        
        // 关闭收银台
        this.onClose();
      }, 2000);
    }
  }
});
