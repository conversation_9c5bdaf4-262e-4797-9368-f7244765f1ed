/* 商品卡片样式 */
.product-card {
  background: #ffffff;
  border-radius: 12px;
  margin: 10px 0;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
  width: 100%;
  box-sizing: border-box;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.store-info {
  display: flex;
  align-items: center;
}

.store-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.product-info {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.product-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #f5f5f5;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.product-calories {
  font-size: 14px;
  color: #666;
}

.product-specs {
  font-size: 14px;
  color: #666;
}

.product-quantity {
  font-size: 14px;
  color: #666;
  text-align: right;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.purchase-actions {
  display: flex;
  gap: 12px;
  width: 100%;
}

.modify-btn {
  flex: 1;
  background: transparent;
  border: 1px solid #1976d2;
  color: #1976d2;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
}

.modify-btn::after {
  border: none;
}

.pay-btn {
  flex: 2;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 10px 16px;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
}

.pay-btn::after {
  border: none;
}

.order-actions {
  width: 100%;
}

.order-detail-btn {
  width: 100%;
  background: #1976d2;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.2;
  text-align: center;
}

.order-detail-btn::after {
  border: none;
}
