.voice-chat-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  /* 防止页面滚动和拖动，但允许特定区域的触摸事件 */
  touch-action: none;
  -webkit-overflow-scrolling: touch;
}

/* 状态栏 */
.status-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
}

/* 自定义导航栏 */
.custom-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
}

.nav-left {
  width: 80px;
  display: flex;
  align-items: center;
}

.nav-back-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.nav-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.nav-right {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 清空按钮样式已移除 */

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止水平滚动 */
  width: 100%;
  box-sizing: border-box;
}

.messages-container {
  min-height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.message-item {
  margin-bottom: 16px;
}

/* 用户消息 */
.user-message-content {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 8px;
}

.user-avatar {
  background: #667eea;
}

/* AI消息 */
.ai-message-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 8px;
}

.ai-avatar {
  background: #52c41a;
}

/* 头像 */
.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 文本消息 */
.text-message {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  word-break: break-word;
  overflow-wrap: break-word;
}

.user-message .text-message {
  background: #667eea;
  color: white;
}

.message-text {
  font-size: 16px;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 语音转文字标签 */
.voice-transcript {
  border: 1px solid #1890ff;
  background: #e6f7ff !important;
  color: #1890ff !important;
}

.transcript-tag {
  position: absolute;
  top: -8px;
  right: 8px;
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-text {
  font-size: 10px;
}

.confidence-indicator {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 1px 3px;
}

.confidence-text {
  font-size: 9px;
  color: white;
}

/* 语音消息相关样式已移除，因为现在直接显示识别的文字 */

/* 时间戳 */
.message-time {
  text-align: center;
  margin: 4px 0;
}

.time-text {
  font-size: 12px;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

/* 底部输入区域 */
.chat-input-area {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  /* 确保在安全区域之上 */
  position: relative;
  z-index: 10;
  /* 允许输入区域的触摸事件 */
  touch-action: manipulation;
}

/* 录音状态 */
.recording-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #fff2e8;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recording-animation {
  display: flex;
  gap: 4px;
}

.recording-dot {
  width: 6px;
  height: 6px;
  background: #ff4d4f;
  border-radius: 50%;
  animation: recording-pulse 1.4s infinite ease-in-out;
}

.recording-dot:nth-child(1) { animation-delay: -0.32s; }
.recording-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes recording-pulse {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.recording-text {
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 4px;
}

.recording-tip {
  color: #999;
  font-size: 12px;
}



/* 输入容器 */
.input-container {
  width: 100%;
}

/* 语音输入模式 */
.voice-input-mode {
  width: 100%;
}

.voice-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

/* 外部键盘图标 */
.keyboard-icon-external {
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.keyboard-icon-external:hover {
  background: #e8e8e8;
}

.keyboard-icon-external:active {
  background: #d0d0d0;
  transform: scale(0.95);
}

.keyboard-icon-external .icon {
  font-size: 18px;
}

/* 文字输入模式 */
.text-input-mode {
  width: 100%;
}

.text-input-wrapper {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 25px;
  padding: 8px 16px;
  gap: 12px;
}

.voice-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #667eea;
  border-radius: 50%;
  cursor: pointer;
}

.voice-icon .icon {
  font-size: 16px;
  color: white;
}

.text-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  line-height: 1.4;
  min-height: 20px;
}

.send-btn {
  padding: 6px 12px;
  background: #667eea;
  color: white;
  border-radius: 12px;
  cursor: pointer;
}

.send-text {
  font-size: 14px;
  color: white;
}

/* 语音按钮 */
.voice-btn {
  flex: 1;
  height: 50px;
  background: #4c6ef5;
  border-radius: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
  /* 允许语音按钮响应触摸事件 */
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.voice-btn.recording {
  background: #4c6ef5;
  height: 60px;
}

/* 正常状态的语音按钮 */
.voice-btn-normal {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.voice-btn-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

.keyboard-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.keyboard-icon .icon {
  font-size: 16px;
}

/* 录音中状态 */
.voice-btn-recording {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 录音动画 */
.recording-animation {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 3px;
  height: 20px;
}

.wave-bar {
  width: 3px;
  background: white;
  border-radius: 2px;
  animation: wave-animation 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) { animation-delay: 0s; height: 8px; }
.wave-bar:nth-child(2) { animation-delay: 0.1s; height: 12px; }
.wave-bar:nth-child(3) { animation-delay: 0.2s; height: 16px; }
.wave-bar:nth-child(4) { animation-delay: 0.3s; height: 20px; }
.wave-bar:nth-child(5) { animation-delay: 0.4s; height: 16px; }
.wave-bar:nth-child(6) { animation-delay: 0.5s; height: 12px; }
.wave-bar:nth-child(7) { animation-delay: 0.6s; height: 8px; }
.wave-bar:nth-child(8) { animation-delay: 0.7s; height: 4px; }

@keyframes wave-animation {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.7;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

.recording-tip {
  font-size: 12px;
  color: white;
  opacity: 0.9;
}

/* 欢迎语样式 */
.welcome-message {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin: 8px 0;
  max-width: 280px;
}

.welcome-header {
  margin-bottom: 16px;
}

.welcome-title {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.welcome-subtitle {
  display: block;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.welcome-suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border-radius: 8px;
  padding: 12px 16px;
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-item:hover {
  background: #f0f0f0;
  border-color: #d0d0d0;
}

.suggestion-item:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

.suggestion-text {
  flex: 1;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
}

.suggestion-arrow {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  margin-left: 8px;
}

.arrow-icon {
  font-size: 12px;
  color: #999;
}

/* 商品卡片消息样式 */
.product-message {
  margin: 10px 0;
  width: 100%;
  max-width: 400px; /* 限制最大宽度，确保按钮不换行 */
  /* 商品卡片组件内部已有样式，这里只需要基础布局 */
}
