.voice-chat-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  /* 防止页面滚动和拖动，但允许特定区域的触摸事件 */
  touch-action: none;
  -webkit-overflow-scrolling: touch;
}

/* 状态栏 */
.status-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
}

/* 自定义导航栏 */
.custom-nav {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  padding: 0 16px;
  position: relative;
}

.nav-left {
  width: 80px;
  display: flex;
  align-items: center;
}

.nav-back-btn {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-back-icon {
  font-size: 24px;
  color: white;
  font-weight: bold;
}

.nav-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-title {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.nav-right {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 清空按钮样式已移除 */

/* 聊天消息区域 */
.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  overflow-x: hidden;
  /* 防止水平滚动 */
  width: 100%;
  box-sizing: border-box;
}

.messages-container {
  min-height: 100%;
  width: 100%;
  box-sizing: border-box;
}

.message-item {
  margin-bottom: 16px;
}

/* 用户消息 */
.user-message-content {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  gap: 8px;
}

.user-avatar {
  background: #667eea;
}

/* AI消息 */
.ai-message-content {
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  gap: 8px;
}

.ai-avatar {
  background: #52c41a;
}

/* 头像 */
.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-text {
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 文本消息 */
.text-message {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  word-break: break-word;
  overflow-wrap: break-word;
}

.user-message .text-message {
  background: #667eea;
  color: white;
}

.message-text {
  font-size: 16px;
  line-height: 1.4;
  word-wrap: break-word;
}

/* 语音转文字标签 */
.voice-transcript {
  border: 1px solid #1890ff;
  background: #e6f7ff !important;
  color: #1890ff !important;
}

.transcript-tag {
  position: absolute;
  top: -8px;
  right: 8px;
  background: #1890ff;
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tag-text {
  font-size: 10px;
}

.confidence-indicator {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  padding: 1px 3px;
}

.confidence-text {
  font-size: 9px;
  color: white;
}

/* 语音消息相关样式已移除，因为现在直接显示识别的文字 */

/* 时间戳 */
.message-time {
  text-align: center;
  margin-top: 4px;
}

.time-text {
  font-size: 12px;
  color: #999;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
}

/* 底部输入区域 */
.chat-input-area {
  background: white;
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  /* 确保在安全区域之上 */
  position: relative;
  z-index: 10;
  /* 允许输入区域的触摸事件 */
  touch-action: manipulation;
}

/* 录音状态 */
.recording-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: #fff2e8;
  border-radius: 8px;
  margin-bottom: 12px;
}

.recording-animation {
  display: flex;
  gap: 4px;
}

.recording-dot {
  width: 6px;
  height: 6px;
  background: #ff4d4f;
  border-radius: 50%;
  animation: recording-pulse 1.4s infinite ease-in-out;
}

.recording-dot:nth-child(1) { animation-delay: -0.32s; }
.recording-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes recording-pulse {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.recording-text {
  color: #ff4d4f;
  font-size: 14px;
  margin-bottom: 4px;
}

.recording-tip {
  color: #999;
  font-size: 12px;
}



/* 输入容器 */
.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

.text-input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20px;
  padding: 8px 16px;
}

.text-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  line-height: 1.4;
  min-height: 20px;
}

.send-btn {
  padding: 6px 12px;
  background: #667eea;
  color: white;
  border-radius: 12px;
  margin-left: 8px;
}

.send-text {
  font-size: 14px;
}

/* 语音按钮 */
.voice-btn {
  position: relative;
  width: 60px;
  height: 60px;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
  /* 允许语音按钮响应触摸事件 */
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.voice-btn.recording {
  background: #ff4d4f;
  transform: scale(1.2);
  box-shadow: 0 4px 16px rgba(255, 77, 79, 0.5);
}

.voice-btn-text {
  font-size: 24px;
  color: white;
  margin-bottom: 2px;
}

.voice-btn-tip {
  font-size: 10px;
  color: white;
  opacity: 0.9;
  text-align: center;
  line-height: 1;
  position: absolute;
  bottom: -20px;
  white-space: nowrap;
}

/* 测试按钮 */
.test-btn {
  width: 60px;
  height: 40px;
  background: #ff9500;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
}

.test-text {
  font-size: 12px;
  color: white;
}
