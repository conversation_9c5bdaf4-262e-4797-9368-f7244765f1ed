Component({
  properties: {
    // 是否显示悬浮窗
    show: {
      type: Boolean,
      value: true
    },
    // 悬浮窗图标
    icon: {
      type: String,
      value: 'chat'
    },
    // 初始位置
    position: {
      type: Object,
      value: {
        right: 30,
        bottom: 100
      }
    }
  },

  data: {
    // 当前位置
    currentPosition: {
      right: 30,
      bottom: 100
    },
    // 是否正在拖拽
    isDragging: false,
    // 拖拽开始位置
    startPosition: {
      x: 0,
      y: 0
    },
    // 屏幕尺寸
    screenWidth: 0,
    screenHeight: 0,
    // 按钮尺寸
    buttonSize: 56
  },

  lifetimes: {
    attached() {
      this.initPosition();
      this.getSystemInfo();
    }
  },

  methods: {
    // 获取系统信息
    getSystemInfo() {
      const systemInfo = wx.getSystemInfoSync();
      this.setData({
        screenWidth: systemInfo.windowWidth,
        screenHeight: systemInfo.windowHeight
      });
    },

    // 初始化位置
    initPosition() {
      this.setData({
        currentPosition: { ...this.properties.position }
      });
    },

    // 开始拖拽
    onTouchStart(e) {
      const touch = e.touches[0];
      this.setData({
        isDragging: true,
        startPosition: {
          x: touch.clientX,
          y: touch.clientY
        }
      });
    },

    // 拖拽中
    onTouchMove(e) {
      if (!this.data.isDragging) return;

      const touch = e.touches[0];
      const { startPosition, currentPosition, screenWidth, screenHeight, buttonSize } = this.data;
      
      // 计算移动距离
      const deltaX = touch.clientX - startPosition.x;
      const deltaY = touch.clientY - startPosition.y;

      // 计算新位置
      let newRight = currentPosition.right - deltaX;
      let newBottom = currentPosition.bottom + deltaY;

      // 边界检测
      newRight = Math.max(0, Math.min(newRight, screenWidth - buttonSize));
      newBottom = Math.max(0, Math.min(newBottom, screenHeight - buttonSize));

      this.setData({
        currentPosition: {
          right: newRight,
          bottom: newBottom
        }
      });
    },

    // 结束拖拽
    onTouchEnd(e) {
      this.setData({
        isDragging: false
      });

      // 自动吸附到边缘
      this.autoAttachToEdge();
    },

    // 自动吸附到边缘
    autoAttachToEdge() {
      const { currentPosition, screenWidth, buttonSize } = this.data;
      const centerX = screenWidth / 2;
      
      // 判断靠近哪一边
      const distanceToLeft = screenWidth - currentPosition.right - buttonSize;
      const distanceToRight = currentPosition.right;

      let newRight = currentPosition.right;
      
      // 吸附到最近的边缘
      if (distanceToLeft < distanceToRight) {
        newRight = 10; // 吸附到左边
      } else {
        newRight = screenWidth - buttonSize - 10; // 吸附到右边
      }

      this.setData({
        currentPosition: {
          ...currentPosition,
          right: newRight
        }
      });
    },

    // 点击事件
    onTap(e) {
      // 如果刚刚结束拖拽，不触发点击事件
      if (this.data.isDragging) return;

      // 触发自定义事件
      this.triggerEvent('tap');
      
      // 跳转到语音对话页面
      wx.navigateTo({
        url: '/pages/voice-chat/index'
      });
    }
  }
});
