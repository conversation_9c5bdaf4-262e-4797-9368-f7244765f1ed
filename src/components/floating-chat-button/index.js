Component({
  properties: {
    // 是否显示悬浮窗
    show: {
      type: Boolean,
      value: true
    },
    // 悬浮窗图标
    icon: {
      type: String,
      value: 'chat'
    },
    // 固定位置（左边）
    position: {
      type: Object,
      value: {
        left: 20,
        bottom: 100
      }
    }
  },

  data: {
    // 固定位置（不可变）
    fixedPosition: {
      left: 20,
      bottom: 100
    }
  },

  lifetimes: {
    attached() {
      this.initPosition();
    }
  },

  methods: {
    // 初始化位置
    initPosition() {
      this.setData({
        fixedPosition: { ...this.properties.position }
      });
    },

    // 点击事件
    onTap() {
      // 触发自定义事件
      this.triggerEvent('tap');

      // 跳转到语音对话页面
      wx.navigateTo({
        url: '/pages/voice-chat/index'
      });
    }
  }
});
