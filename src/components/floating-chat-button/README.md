# 悬浮窗聊天按钮组件

一个固定在页面左边的悬浮窗聊天按钮组件，点击后跳转到语音对话页面。

## 功能特性

- ✅ 固定在页面左边，不可拖拽
- ✅ 呼吸动画效果
- ✅ 点击跳转到语音对话页面
- ✅ 可配置显示/隐藏
- ✅ 全局管理器统一控制
- ✅ 防止意外移动和选择

## 使用方法

### 1. 基本使用

在页面的 wxml 文件中添加组件：

```xml
<floating-chat-button
  wx:if="{{floatingChatVisible}}"
  show="{{floatingChatVisible}}"
  bindtap="onFloatingChatTap"
/>
```

在页面的 js 文件中引入混入：

```javascript
const floatingChatMixin = require('../../mixins/floating-chat-mixin');

Page({
  // 混入悬浮窗功能
  ...floatingChatMixin,

  data: {
    ...floatingChatMixin.data,
    // 页面自己的数据
  },

  onLoad(options) {
    // 调用混入的onLoad方法
    if (floatingChatMixin.onLoad) {
      floatingChatMixin.onLoad.call(this, options);
    }
  },

  onShow() {
    // 调用混入的onShow方法
    if (floatingChatMixin.onShow) {
      floatingChatMixin.onShow.call(this);
    }
  }
});
```

### 2. 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| show | Boolean | true | 是否显示悬浮窗 |
| icon | String | 'chat' | 悬浮窗图标类型 |
| position | Object | {left: 20, bottom: 100} | 固定位置（左边） |

### 3. 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| tap | 点击悬浮窗时触发 | - |

### 4. 全局管理

使用全局管理器可以统一控制所有页面的悬浮窗显示：

```javascript
const floatingChatManager = require('../../utils/floating-chat-manager');

// 启用悬浮窗
floatingChatManager.enable();

// 禁用悬浮窗
floatingChatManager.disable();

// 切换悬浮窗状态
floatingChatManager.toggle();

// 添加排除页面（不显示悬浮窗的页面）
floatingChatManager.addExcludePage('pages/some-page/index');

// 获取当前状态
const status = floatingChatManager.getStatus();
```

### 5. 页面混入方法

混入提供了以下方法：

```javascript
// 临时隐藏悬浮窗
this.temporaryHideFloatingChat();

// 恢复显示悬浮窗
this.restoreFloatingChat();

// 切换悬浮窗显示状态
this.toggleFloatingChat();

// 注意：位置相关方法已移除，因为悬浮窗现在固定在左边
```

## 语音对话页面

悬浮窗点击后会跳转到 `/pages/voice-chat/index` 页面，该页面提供：

- 📝 文本消息输入和发送
- 🎤 语音录制和播放
- 🔄 语音转文字功能
- 🤖 AI 模拟回复
- 💾 聊天记录本地存储

## 演示页面

访问 `/pages/demo-floating-chat/index` 可以查看完整的演示效果。

## 注意事项

1. 悬浮窗会自动排除以下页面：
   - `pages/voice-chat/index` (语音对话页面本身)
   - `pages/account/login/index` (登录页面)
   - `pages/pay/unicashier/index` (支付页面)

2. 悬浮窗现在固定在页面左边，不可拖拽移动

3. 语音功能需要用户授权录音权限

4. 语音识别功能已集成微信小程序真实API

## 自定义样式

可以通过修改 `index.wxss` 文件来自定义悬浮窗的样式：

```css
.floating-chat-button {
  /* 自定义样式 */
}

.button-content {
  /* 自定义按钮内容样式 */
}
```
