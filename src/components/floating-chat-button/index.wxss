.floating-chat-button {
  position: fixed;
  width: 56px;
  height: 56px;
  z-index: 9999;
  cursor: pointer;
}

/* 固定在左边的样式 */
.floating-chat-button.fixed-left {
  /* 禁用拖拽和选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  pointer-events: auto;
  /* 确保按钮不会被意外移动 */
  transform: none !important;
}

.button-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.button-content:active {
  transform: scale(0.95);
}

.chat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 24px;
  color: white;
}

/* 呼吸动画效果 */
.ripple-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid rgba(102, 126, 234, 0.6);
  animation: ripple-animation 2s infinite;
  transform-origin: center center;
  transform: translate(-50%, -50%);
}

.ripple-1 {
  animation-delay: 0s;
}

.ripple-2 {
  animation-delay: 1s;
}

@keyframes ripple-animation {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* 悬停效果 */
.floating-chat-button:hover .button-content {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

/* 点击效果 */
.floating-chat-button:active .button-content {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}
